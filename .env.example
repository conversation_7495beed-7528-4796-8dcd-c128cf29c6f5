# Hollywood Table Backend API Environment Variables
# Copy this file to .env and fill in your actual values

# =================================
# APPLICATION CONFIGURATION
# =================================

# Environment (development, production)
NODE_ENV=development

# Application URL - used for redirects and CORS
SITE_URL=http://localhost:3000

# Server port (optional - defaults to 3000)
PORT=3001

# =================================
# SUPABASE CONFIGURATION
# =================================

# Your Supabase project URL
SUPABASE_URL=https://your-project-id.supabase.co

# Supabase service role key (secret key with admin privileges)
# This is used for server-side operations that bypass RLS
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =================================
# OPENAI CONFIGURATION
# =================================

# OpenAI API key for chat completions
# Used for AI character dialogue generation
OPENAI_API_KEY=sk-your_openai_api_key_here

# =================================
# STRIPE CONFIGURATION
# =================================

# Stripe test secret key (for development)
STRIPE_TEST_SECRET_KEY=sk_test_your_stripe_test_secret_key_here

# Stripe live secret key (for production)
STRIPE_LIVE_SECRET_KEY=sk_live_your_stripe_live_secret_key_here

# =================================
# NOTES
# =================================

# 1. The application automatically switches between test/live Stripe keys based on NODE_ENV
# 2. In development mode (localhost), the chat API uses random responses instead of OpenAI
# 3. SITE_URL is used for Stripe checkout redirects and email confirmation links
# 4. The SUPABASE_SERVICE_ROLE_KEY is required for user subscription management
# 5. All environment variables are required for full functionality

# =================================
# DEVELOPMENT SETUP
# =================================

# For local development:
# 1. Set NODE_ENV=development
# 2. Use your Supabase test/development project
# 3. Use Stripe test keys
# 4. Set SITE_URL to your frontend URL (usually http://localhost:3000)

# =================================
# PRODUCTION SETUP
# =================================

# For production deployment:
# 1. Set NODE_ENV=production
# 2. Use your Supabase production project
# 3. Use Stripe live keys
# 4. Set SITE_URL to your production domain (e.g., https://hollywoodtable.com)
