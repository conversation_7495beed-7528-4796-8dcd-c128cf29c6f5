# Hollywood Table Backend API

This is the migrated backend API for the Hollywood Table application, built with Hon<PERSON> and <PERSON>un.

## Migration Status

✅ **Complete** - All APIs from `supabase/functions/api/index.ts` have been successfully migrated to this Hono backend.

### Migrated Features

- **Chat API** - `/api/chat` - Complete with OpenAI integration and development mode
- **Usage Tracking** - `/api/usage/check-limits` - Message limit checking with PAYG support
- **Billing System** - `/api/billing/*` - Full subscription management, Stripe integration
- **Waitlist** - `/api/waitlist/send-acknowledgment` - Email acknowledgment system
- **Admin** - `/api/admin/invite-users` - User invitation system
- **Health Check** - `/api/health` - System status endpoint

## Project Structure

```
backend/
├── src/
│   ├── shared/           # Shared utilities (migrated from _shared/)
│   │   ├── logger.ts     # Logging utility
│   │   ├── supabase.ts   # Supabase client and auth utilities
│   │   ├── stripe.ts     # Stripe client configuration
│   │   ├── pricing.ts    # Pricing and tier configuration
│   │   └── cors.ts       # CORS configuration
│   ├── routes/           # Route modules
│   │   └── billing.ts    # Billing-related endpoints
│   └── index.ts          # Main application entry point
├── package.json          # Dependencies and scripts
└── README.md            # This file
```

## Environment Variables

Create a `.env` file in the backend directory with the following variables:

```bash
# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Stripe Configuration
STRIPE_TEST_SECRET_KEY=your_stripe_test_secret_key
STRIPE_LIVE_SECRET_KEY=your_stripe_live_secret_key

# Application Configuration
NODE_ENV=development
SITE_URL=http://localhost:3000

# Port (optional - defaults to 3000)
PORT=3001
```

## Installation & Setup

```bash
# Install dependencies
bun install

# Development mode (with hot reload)
bun run dev

# Build for production
bun run build

# Start production server
bun run start
```

## API Endpoints

### Chat
- `POST /api/chat` - Send message to AI character

### Usage
- `POST /api/usage/check-limits` - Check user message limits

### Billing
- `GET /api/billing/subscription` - Get user subscription details
- `POST /api/billing/customer-portal` - Get Stripe customer portal URL
- `POST /api/billing/purchase-credits` - Purchase PAYG credits
- `POST /api/billing/manage-subscription` - Manage subscription tiers

### Waitlist
- `POST /api/waitlist/send-acknowledgment` - Send waitlist acknowledgment email

### Admin
- `POST /api/admin/invite-users` - Invite users to beta

### System
- `GET /api/health` - Health check endpoint
- `GET /` - Welcome message

## Key Changes from Supabase Functions

1. **Runtime Environment**: Migrated from Deno to Node.js/Bun
2. **Framework**: Using Hono instead of native Deno serve
3. **Imports**: Changed from JSR imports to NPM packages
4. **Environment Variables**: Using `process.env` instead of `Deno.env`
5. **Error Handling**: Improved TypeScript error handling
6. **Code Organization**: Separated routes into modules for better maintainability

## Development Features

- **Hot Reload**: Automatic restart on file changes in development
- **Development Mode**: Uses random responses instead of OpenAI when running locally
- **TypeScript**: Full type safety with proper error handling
- **CORS**: Configured for frontend integration
- **Logging**: Structured logging with the `logStep` utility

## Test Environment Support

The backend now supports automatic test environment detection and management:

### Usage

Send the `x-env` header with your requests to control which environment is used:

```bash
# Use test environment (data goes to _test_ prefixed tables)
curl -H "x-env: test" -H "authorization: Bearer <token>" \
  http://localhost:3001/api/chat

# Use production environment (default behavior)
curl -H "x-env: live" -H "authorization: Bearer <token>" \
  http://localhost:3001/api/chat

# No header defaults to production environment
curl -H "authorization: Bearer <token>" \
  http://localhost:3001/api/chat
```

### How It Works

1. **Environment Detection**: Middleware automatically detects the `x-env` header
2. **Table Resolution**: Database operations automatically use `_test_` prefixed tables in test mode
3. **Auto Table Creation**: Test tables are automatically created when needed
4. **Schema Synchronization**: Test tables maintain the same structure as production tables

### Test Tables

When `x-env: test` is used, the following tables are automatically created and used:
- `_test_user_subscriptions`
- `_test_characters`
- `_test_conversations`
- `_test_messages`
- `_test_waitlist`
- `_test_invite_codes`
- `_test_subscription_metrics`
- `_test_subscription_change_history`

## Production Considerations

- Set `NODE_ENV=production` for production deployment
- Ensure all environment variables are properly configured
- Use live Stripe keys for production
- Configure proper CORS origins for production
- Test environment adds minimal overhead to production requests

## Testing

```bash
# Run unit tests
bun test

# Run tests in watch mode
bun test:watch
```

The API maintains the same interface as the original Supabase function, so existing frontend code should work without changes. The main difference is the base URL will change from the Supabase function URL to your backend server URL.

## Next Steps

1. Deploy the backend to your preferred hosting platform
2. Update frontend API calls to point to the new backend URL
3. Test all functionality in both development and production environments
4. Use the test environment for development and testing
5. Consider adding additional middleware for rate limiting, authentication, etc.
