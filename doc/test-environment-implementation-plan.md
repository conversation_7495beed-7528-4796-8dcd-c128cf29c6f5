# Test Environment Implementation Plan

## Overview

This document outlines the comprehensive plan to implement test environment support in the Hollywood Table Backend API. The system will automatically detect the `x-env` header and route database operations to test tables (with `_test_` prefix) when `x-env=test`, while using production tables for `x-env=live` or no header.

## Current Database Tables Identified

Based on codebase analysis, the following tables are currently used:
- `user_subscriptions` - User subscription data
- `characters` - AI character configurations  
- `conversations` - Chat conversation records
- `messages` - Individual chat messages
- `waitlist` - User waitlist entries
- `invite_codes` - Beta invitation codes
- `subscription_metrics` - Analytics data
- `subscription_change_history` - Subscription change tracking

## Architecture Design

### 1. Environment Detection Middleware

**Location**: `src/middleware/environment.ts`

**Functionality**:
- Intercept all incoming requests
- Check for `x-env` header
- Set environment context in request object
- Default to `production` if header missing or `x-env=live`
- Set to `test` if `x-env=test`

**Implementation**:
```typescript
interface EnvironmentContext {
  environment: 'test' | 'production';
  tablePrefix: string;
}

// Middleware function to detect environment
export const environmentMiddleware = async (c: Context, next: Next) => {
  const envHeader = c.req.header('x-env');
  const environment = envHeader === 'test' ? 'test' : 'production';
  const tablePrefix = environment === 'test' ? '_test_' : '';
  
  c.set('environment', { environment, tablePrefix });
  await next();
};
```

### 2. Universal Database Utility Functions

**Location**: `src/shared/database-utils.ts`

**Core Functions**:

#### Table Name Resolution
```typescript
export const getTableName = (baseTableName: string, context: EnvironmentContext): string => {
  return context.environment === 'test' ? `_test_${baseTableName}` : baseTableName;
};
```

#### Test Table Creation/Synchronization
```typescript
export const ensureTestTable = async (baseTableName: string, supabaseClient: SupabaseClient): Promise<void> => {
  // Check if test table exists
  // If not, create it with same structure as base table
  // Sync any schema changes from base table
};
```

#### Schema Synchronization
```typescript
export const syncTableSchema = async (baseTableName: string, supabaseClient: SupabaseClient): Promise<void> => {
  // Compare base table schema with test table schema
  // Apply any missing columns, indexes, constraints to test table
  // Preserve test data while updating structure
};
```

### 3. Environment-Aware Supabase Client

**Location**: `src/shared/supabase-enhanced.ts`

**Enhanced Client Functions**:
```typescript
export const createEnvironmentAwareSupabaseClient = (authToken?: string, environment?: EnvironmentContext) => {
  const client = createSupabaseClient(authToken);
  
  // Wrap the from() method to automatically use correct table names
  const originalFrom = client.from.bind(client);
  client.from = (tableName: string) => {
    const actualTableName = environment ? getTableName(tableName, environment) : tableName;
    return originalFrom(actualTableName);
  };
  
  return client;
};
```

### 4. Database Migration System

**Location**: `src/shared/migration-utils.ts`

**Migration Functions**:
- `createAllTestTables()` - Create test versions of all existing tables
- `syncAllTestTables()` - Sync schema changes to all test tables
- `cleanupTestData()` - Clear test data while preserving structure
- `validateTestEnvironment()` - Ensure test tables are properly configured

## Implementation Phases

### Phase 1: Core Infrastructure (Tasks 1-3)
1. **Environment Detection Middleware**
   - Create middleware to detect x-env header
   - Add environment context to request object
   - Integrate with Hono app

2. **Universal Database Utilities**
   - Implement table name resolution functions
   - Create test table creation utilities
   - Build schema synchronization functions

3. **Enhanced Supabase Client**
   - Wrap Supabase client with environment awareness
   - Automatic table name resolution
   - Backward compatibility with existing code

### Phase 2: Integration (Tasks 4-5)
4. **Update Existing Database Operations**
   - Modify all `.from()` calls to use enhanced client
   - Update stored procedure calls for test environment
   - Ensure RPC functions work with test tables

5. **Migration and Sync Utilities**
   - Build initial test table creation scripts
   - Implement ongoing schema synchronization
   - Create data cleanup utilities

### Phase 3: Testing and Documentation (Tasks 6-8)
6. **Comprehensive Testing**
   - Unit tests for middleware and utilities
   - Integration tests for database operations
   - End-to-end tests with both environments

7. **Documentation and Examples**
   - Update README with test environment usage
   - Create developer guide for test environment
   - Add API documentation for new headers

## Technical Considerations

### Database Schema Management
- Test tables will mirror production table structure exactly
- Automatic schema synchronization on application startup
- Foreign key relationships maintained within test environment
- Indexes and constraints replicated to test tables

### Data Isolation
- Complete separation between test and production data
- Test data automatically expires/cleans up
- No cross-environment data leakage
- Separate RLS policies for test tables

### Performance Considerations
- Minimal overhead for production requests (no header check impact)
- Efficient table name resolution caching
- Lazy test table creation (only when needed)
- Background schema synchronization

### Security and Access Control
- Test environment inherits same RLS policies
- Service role access for test table management
- Audit logging for test environment usage
- Separate monitoring for test vs production

## Configuration and Environment Variables

### New Environment Variables
```bash
# Test Environment Configuration
ENABLE_TEST_ENVIRONMENT=true
TEST_TABLE_PREFIX=_test_
AUTO_SYNC_TEST_SCHEMA=true
TEST_DATA_RETENTION_DAYS=7
```

### Header Specifications
- `x-env: test` - Use test environment
- `x-env: live` - Use production environment (explicit)
- No header - Default to production environment

## Rollout Strategy

### Development Phase
1. Implement core utilities and middleware
2. Create test tables for critical tables first
3. Gradual migration of database operations
4. Extensive testing in development environment

### Staging Phase
1. Deploy to staging environment
2. Test with real-world scenarios
3. Performance testing and optimization
4. Documentation and training materials

### Production Phase
1. Feature flag controlled rollout
2. Monitor performance impact
3. Gradual enablement for different user groups
4. Full production deployment

## Success Metrics

### Functional Metrics
- 100% of database operations support test environment
- Zero data leakage between environments
- Schema synchronization accuracy > 99.9%
- Test table creation time < 5 seconds

### Performance Metrics
- Production request latency impact < 1ms
- Test environment request latency < 2x production
- Memory overhead < 10MB
- CPU overhead < 5%

### Operational Metrics
- Test environment uptime > 99%
- Schema sync success rate > 99.9%
- Test data cleanup success rate > 99%
- Developer adoption rate > 80%

## Risk Mitigation

### Data Safety
- Comprehensive backup strategy for test data
- Rollback procedures for schema changes
- Data validation checks before sync
- Emergency isolation procedures

### Performance Impact
- Caching strategies for table name resolution
- Lazy loading of test table metadata
- Background processing for heavy operations
- Circuit breakers for failing operations

### Operational Risks
- Monitoring and alerting for test environment
- Automated health checks
- Graceful degradation strategies
- Clear escalation procedures

## Next Steps

1. Review and approve this implementation plan
2. Set up development environment for testing
3. Begin Phase 1 implementation
4. Establish testing and validation procedures
5. Create detailed technical specifications for each component

---

*This plan provides a comprehensive roadmap for implementing test environment support while maintaining system reliability, performance, and security.*
