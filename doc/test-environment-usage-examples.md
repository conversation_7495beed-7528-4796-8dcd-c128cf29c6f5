# Test Environment Usage Examples

This document provides practical examples of how to use the test environment functionality in the Hollywood Table Backend API.

## Basic Usage

### Frontend Integration

```javascript
// Example: Frontend API client with environment support
class HollywoodTableAPI {
  constructor(baseURL, authToken, environment = 'production') {
    this.baseURL = baseURL;
    this.authToken = authToken;
    this.environment = environment;
  }

  // Get headers with environment support
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.authToken}`
    };

    // Add x-env header for test environment
    if (this.environment === 'test') {
      headers['x-env'] = 'test';
    }

    return headers;
  }

  // Send chat message
  async sendMessage(character, message, conversationId = null) {
    const response = await fetch(`${this.baseURL}/api/chat`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        character,
        user_chat: message,
        conversation_id: conversationId
      })
    });

    return response.json();
  }

  // Check usage limits
  async checkUsageLimits() {
    const response = await fetch(`${this.baseURL}/api/usage/check-limits`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        action_type: 'message'
      })
    });

    return response.json();
  }
}

// Usage examples
const prodAPI = new HollywoodTableAPI('https://api.hollywoodtable.com', 'your-token', 'production');
const testAPI = new HollywoodTableAPI('https://api.hollywoodtable.com', 'your-token', 'test');

// Production request (uses normal tables)
await prodAPI.sendMessage('character-id', 'Hello!');

// Test request (uses _test_ prefixed tables)
await testAPI.sendMessage('character-id', 'Hello from test!');
```

### cURL Examples

```bash
# Production environment (default)
curl -X POST https://api.hollywoodtable.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "character": "character-id",
    "user_chat": "Hello from production!"
  }'

# Test environment
curl -X POST https://api.hollywoodtable.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -H "x-env: test" \
  -d '{
    "character": "character-id",
    "user_chat": "Hello from test environment!"
  }'

# Explicit production environment
curl -X POST https://api.hollywoodtable.com/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -H "x-env: live" \
  -d '{
    "character": "character-id",
    "user_chat": "Hello from explicit production!"
  }'
```

## Development Workflow

### Local Development Setup

```bash
# 1. Start the backend in development mode
bun run dev

# 2. Use test environment for all development requests
export TEST_API_URL="http://localhost:3001"
export TEST_TOKEN="your-dev-token"

# 3. Test chat functionality
curl -X POST $TEST_API_URL/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TEST_TOKEN" \
  -H "x-env: test" \
  -d '{
    "character": "test-character",
    "user_chat": "Testing in development!"
  }'
```

### Testing Different Scenarios

```javascript
// Example: Automated testing with different environments
describe('API Environment Tests', () => {
  const api = new HollywoodTableAPI('http://localhost:3001', 'test-token');

  test('should isolate test data from production', async () => {
    // Send message in test environment
    api.environment = 'test';
    const testResponse = await api.sendMessage('char-1', 'Test message');
    
    // Send message in production environment
    api.environment = 'production';
    const prodResponse = await api.sendMessage('char-1', 'Prod message');
    
    // Verify responses are different (different conversation IDs, etc.)
    expect(testResponse.conversation_id).not.toBe(prodResponse.conversation_id);
  });

  test('should handle missing test tables gracefully', async () => {
    api.environment = 'test';
    
    // First request should create test tables automatically
    const response = await api.sendMessage('char-1', 'First test message');
    expect(response.error).toBeUndefined();
  });
});
```

## Database Management

### Manual Test Environment Management

```javascript
// Example: Backend utility functions for test environment management
import { createSupabaseClient } from './shared/supabase';
import { 
  initializeTestEnvironment,
  resetTestEnvironment,
  validateTestEnvironment,
  getTestEnvironmentStatus
} from './shared/migration-utils';

// Initialize test environment
async function setupTestEnvironment() {
  const client = createSupabaseClient();
  
  console.log('Initializing test environment...');
  const result = await initializeTestEnvironment(client);
  
  if (result.success) {
    console.log('✅ Test environment initialized successfully');
    console.log(`Created ${result.details.tablesCreated.length} test tables`);
  } else {
    console.error('❌ Failed to initialize test environment:', result.message);
  }
}

// Reset test environment (clean data)
async function cleanTestEnvironment() {
  const client = createSupabaseClient();
  
  console.log('Cleaning test environment...');
  const result = await resetTestEnvironment(client, false);
  
  if (result.success) {
    console.log('✅ Test environment cleaned successfully');
  } else {
    console.error('❌ Failed to clean test environment:', result.message);
  }
}

// Validate test environment
async function checkTestEnvironment() {
  const client = createSupabaseClient();
  
  console.log('Validating test environment...');
  const validation = await validateTestEnvironment(client);
  
  if (validation.isValid) {
    console.log('✅ Test environment is valid');
    if (validation.warnings.length > 0) {
      console.warn('⚠️ Warnings:', validation.warnings);
    }
  } else {
    console.error('❌ Test environment validation failed');
    console.error('Issues:', validation.issues);
  }
}

// Get test environment status
async function getTestStatus() {
  const client = createSupabaseClient();
  
  const status = await getTestEnvironmentStatus(client);
  console.log('Test Environment Status:', {
    totalTables: status.summary.totalTables,
    existingTables: status.summary.existingTables,
    tablesWithData: status.summary.tablesWithData,
    isValid: status.validation.isValid
  });
}
```

### CLI Commands

```bash
# Add these scripts to package.json for easy test environment management
{
  "scripts": {
    "test:env:init": "bun run scripts/init-test-env.ts",
    "test:env:clean": "bun run scripts/clean-test-env.ts",
    "test:env:status": "bun run scripts/test-env-status.ts",
    "test:env:validate": "bun run scripts/validate-test-env.ts"
  }
}

# Usage
bun run test:env:init     # Initialize test environment
bun run test:env:clean    # Clean test data
bun run test:env:status   # Check test environment status
bun run test:env:validate # Validate test environment
```

## Integration with CI/CD

### GitHub Actions Example

```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        
      - name: Install dependencies
        run: bun install
        
      - name: Setup test environment
        run: |
          # Set up test database and environment variables
          export SUPABASE_URL=${{ secrets.TEST_SUPABASE_URL }}
          export SUPABASE_SERVICE_ROLE_KEY=${{ secrets.TEST_SUPABASE_KEY }}
          bun run test:env:init
        
      - name: Run tests
        run: |
          # Run tests with test environment
          export NODE_ENV=test
          bun test
        
      - name: Clean up test environment
        run: bun run test:env:clean
```

## Best Practices

### 1. Environment Separation

```javascript
// Always use test environment for automated tests
const getAPIClient = (environment = 'test') => {
  return new HollywoodTableAPI(
    process.env.API_URL,
    process.env.API_TOKEN,
    environment
  );
};

// Development
const devAPI = getAPIClient('test');

// Production (only for production deployments)
const prodAPI = getAPIClient('production');
```

### 2. Data Isolation

```javascript
// Example: Test data setup and teardown
beforeEach(async () => {
  // Clean test environment before each test
  const client = createSupabaseClient();
  await resetTestEnvironment(client, false);
});

afterAll(async () => {
  // Clean up after all tests
  const client = createSupabaseClient();
  await resetTestEnvironment(client, false);
});
```

### 3. Environment Detection

```javascript
// Automatically detect environment based on context
const getEnvironment = () => {
  if (process.env.NODE_ENV === 'test') return 'test';
  if (process.env.NODE_ENV === 'development') return 'test';
  return 'production';
};

const api = new HollywoodTableAPI(
  process.env.API_URL,
  process.env.API_TOKEN,
  getEnvironment()
);
```

## Troubleshooting

### Common Issues

1. **Test tables not created**: Ensure the backend has proper database permissions
2. **Data leakage**: Verify `x-env` header is being sent correctly
3. **Schema mismatches**: Run test environment validation to check for issues
4. **Performance issues**: Test environment operations may be slower due to table creation

### Debugging

```javascript
// Enable debug logging for test environment
process.env.DEBUG = 'test-environment';

// Check which tables are being used
const client = createEnvironmentAwareSupabaseClientFromContext(context);
console.log('Environment:', client.environment);
console.log('Using test tables:', client.environment.isTestEnvironment);
```

This comprehensive guide should help developers effectively use the test environment functionality for development, testing, and CI/CD workflows.
