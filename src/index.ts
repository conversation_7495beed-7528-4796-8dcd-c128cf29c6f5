import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { nanoid } from 'nanoid'
import { createSupabaseClient, createSupabaseClientFromContext, getAuthenticatedUser, ensureUserSubscription } from './shared/supabase'
import { logStep } from './shared/logger'
import { createStripeClient, Stripe } from './shared/stripe'
import { getStripePriceId, getPaygPriceId, PAYG_PACKAGES } from './shared/pricing'
import billing from './routes/billing'

const app = new Hono()

// Add CORS middleware
app.use("/*", cors({
  origin: "*",
  allowHeaders: ["authorization", "x-client-info", "apikey", "content-type"],
}))

// Constants and configurations
const OPENAI_API_KEY = process.env.OPENAI_API_KEY

// Development mode random text responses
const RANDOM_RESPONSES = [
  "Ah, darling, you've got that look in your eyes - the same one I had when I first walked into this godforsaken city. Tell me, what brings you to my table tonight?",
  "The smoke from my cigarette curls like secrets in the dim light. I've seen things in this town that would make a saint reach for the bottle. What's your poison?",
  "Listen here, sweetheart, in this business you learn to read people like yesterday's newspaper. And you? You're front page material.",
  "The rain's been falling on this city for three days straight, washing away the sins but never quite getting them clean. What's eating at you?",
  "I've been in this racket long enough to know when someone's carrying more than they can handle. Spill it, and maybe we can both sleep better tonight.",
  "The neon lights outside paint everything in shades of desperation. But you, you've got something different about you. What's your story?",
  "In this town, everybody's got an angle, everybody's working an angle. The question is - what's yours?",
  "The jazz from the club downstairs seeps through the floorboards like a heartbeat. It's the only honest thing left in this city. Now, what can you tell me?"
]

function generateRandomResponse(): string {
  return RANDOM_RESPONSES[Math.floor(Math.random() * RANDOM_RESPONSES.length)]
}

function isDevelopmentMode(origin: string | undefined): boolean {
  if (!origin) return false
  return origin.includes('localhost') || origin.includes('127.0.0.1') || origin.includes('0.0.0.0')
}

// ================================
// CHAT ROUTES (/api/chat/*)
// ================================

app.post("/api/chat", async (c) => {
  try {
    logStep("api", "Starting chat request")

    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ 
        error: "User not authenticated.",
        details: authError 
      }, 401)
    }

    const { character, conversation_id: requestConversationId, user_chat } = await c.req.json()
    const originalConversationId = requestConversationId
    let conversation_id = requestConversationId
    
    if (!character || !user_chat) {
      return c.json({
        error: "Character and user_chat are required."
      }, 400)
    }

    logStep("api", "User authenticated for chat", { userId: user.id })

    // Ensure user has subscription record and check usage limits
    try {
      const supabaseClient = createSupabaseClientFromContext(c)
      
      // Ensure user has a subscription record before checking limits
      try {
        await ensureUserSubscription(user.id)
      } catch (subscriptionError) {
        logStep("api", "Failed to ensure user subscription", { error: (subscriptionError as Error).message })
        return c.json({
          error: "Unable to initialize user subscription. Please try again.",
          details: (subscriptionError as Error).message
        }, 500)
      }
      
      // Always check message limits (no longer tracking conversations)
      const { data: actionResult, error: actionError } = await supabaseClient
        .rpc('can_user_perform_action', {
          user_uuid: user.id,
          action_type: 'message'
        })

      if (actionError) {
        logStep("api", "Usage check failed", { error: actionError })
        return c.json({
          error: "Unable to verify usage limits. Please try again.",
          details: actionError.message
        }, 500)
      }

      const result = actionResult[0]
      if (!result.can_perform) {
        logStep("api", "Usage limit exceeded", { reason: result.reason })
        return c.json({
          error: "message_limit_exceeded",
          message: result.reason,
          upgradeRequired: true
        }, 429)
      }

      logStep("api", "Usage limits passed", { 
        canPerform: result.can_perform,
        usingPayg: result.using_payg
      })

    } catch (error) {
      logStep("api", "Usage check error", { error: (error as Error).message })
    }

    const supabaseClient = createSupabaseClient(c.req.header('authorization'))

    // Fetch character data
    const { data: characterData, error: characterError } = await supabaseClient
      .from('characters')
      .select('system_prompt, name')
      .eq('id', character)
      .single()
      
    if (characterError || !characterData) {
      return c.json({
        error: "Character not found.",
        details: characterError?.message
      }, 404)
    }

    const messages: Array<{role: string, content: string}> = []

    if (conversation_id) {
      const { data: conversationData, error: conversationError } = await supabaseClient
        .from('conversations')
        .select('id')
        .eq('id', conversation_id)
        .single()
        
      if (conversationError || !conversationData) {
        return c.json({ error: "Conversation not found." }, 404)
      }

      const { data: conversationMessages, error: messagesError } = await supabaseClient
        .from('messages')
        .select('sender_type, content')
        .eq('conversation_id', conversation_id)
        .order('created_at', { ascending: true })
        
      if (messagesError) {
        return c.json({
          error: "Error fetching messages.",
          details: messagesError.message
        }, 500)
      }

      if (conversationMessages) {
        conversationMessages.forEach((msg) => {
          messages.push({
            role: msg.sender_type === "user" ? "user" : "assistant",
            content: msg.content
          })
        })
      }
    }

    messages.push({
      role: "user",
      content: user_chat
    })

    // Check if we're in development mode
    const origin = c.req.header("origin")
    const isDevMode = isDevelopmentMode(origin)
    
    let aiResponseContent: string

    if (isDevMode) {
      logStep("api", "Development mode detected - using random response", { 
        messageCount: messages.length + 1,
        character: characterData.name,
        origin: origin
      })
      
      // Simulate some delay to make it feel more realistic
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
      
      aiResponseContent = generateRandomResponse()
    } else {
      logStep("api", "Calling OpenAI API", { 
        messageCount: messages.length + 1,
        character: characterData.name 
      })

      // Call OpenAI
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: "gpt-4o",
          messages: [
            {
              role: "system",
              content: characterData.system_prompt
            },
            ...messages
          ]
        })
      })

      const openAIResponse = await response.json()
      if (!response.ok) {
        logStep("api", "OpenAI API error", { error: openAIResponse })
        return c.json({
          error: "OpenAI API error",
          details: openAIResponse
        }, response.status as any)
      }

      aiResponseContent = openAIResponse.choices[0].message.content || ""
    }
    
    // Create conversation if needed
    if (!conversation_id) {
      const { data: insertedConversation, error: insertConversationError } = await supabaseClient
        .from('conversations')
        .insert({
          character_id: character,
          character_name: characterData.name,
          user_id: user.id,
          title: `Chat with ${characterData.name}`,
          last_message_preview: aiResponseContent.slice(0, 100) + "...",
          message_count: 1
        })
        .select()
        
      if (insertConversationError) {
        return c.json({ error: insertConversationError.message }, 500)
      }
      
      conversation_id = insertedConversation[0].id
    }

    // Save messages
    const { data: userMessageData, error: saveUserMessageError } = await supabaseClient
      .from('messages')
      .insert({
        conversation_id: conversation_id,
        sender_type: "user",
        content: user_chat
      })
      .select('id')
      .single()

    const { data: assistantMessageData, error: saveAssistantMessageError } = await supabaseClient
      .from('messages')
      .insert({
        conversation_id: conversation_id,
        sender_type: "character",
        content: aiResponseContent
      })
      .select('id')
      .single()

    if (saveUserMessageError || saveAssistantMessageError) {
      return c.json({
        error: "Failed to save messages.",
        userError: saveUserMessageError?.message,
        assistantError: saveAssistantMessageError?.message
      }, 500)
    }

    // Usage tracking is now handled automatically by the track_message_usage trigger
    // No manual refresh needed - trigger updates counts when messages are inserted
    logStep("api", "Messages saved, trigger will handle usage tracking automatically")

    return c.json({
      conversation_id: conversation_id,
      user_message_id: userMessageData.id,
      assistant_message_id: assistantMessageData.id,
      openai_response: aiResponseContent
    })

  } catch (error) {
    logStep("api", "Internal server error in chat", { error: (error as Error).message })
    return c.json({ error: "Internal server error" }, 500)
  }
})

// ================================
// USAGE ROUTES (/api/usage/*)
// ================================

app.post("/api/usage/check-limits", async (c) => {
  try {
    logStep("api", "Starting check-usage-limits")

    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401)
    }

    const { action_type } = await c.req.json()
    
    if (!action_type || action_type !== 'message') {
      return c.json({ 
        error: 'Invalid action_type. Must be "message"' 
      }, 400)
    }

    const supabaseClient = createSupabaseClientFromContext(c)
    
    // Ensure user has a subscription record before checking limits
    try {
      await ensureUserSubscription(user.id)
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in usage check", { error: (subscriptionError as Error).message })
      return c.json({
        error: "Unable to initialize user subscription. Please try again.",
        details: (subscriptionError as Error).message
      }, 500)
    }
    
    const { data: actionResult, error: actionError } = await supabaseClient
      .rpc('can_user_perform_action', {
        user_uuid: user.id,
        action_type: action_type
      })

    if (actionError) {
      logStep("api", "Error checking user action", { error: actionError })
      return c.json({ error: 'Failed to check usage limits' }, 500)
    }

    const result = actionResult[0]
    
    return c.json({
      can_perform: result.can_perform,
      reason: result.reason,
      using_payg: result.using_payg,
      subscription_info: result.subscription_info
    })

  } catch (error) {
    logStep("api", "Internal server error in usage check", { error: (error as Error).message })
    return c.json({ error: 'Internal server error' }, 500)
  }
})

// Mount billing routes
app.route('/api/billing', billing)

// ================================
// WAITLIST ROUTES (/api/waitlist/*)
// ================================

app.post("/api/waitlist/send-acknowledgment", async (c) => {
  try {
    const { email } = await c.req.json()
    
    if (!email) {
      return c.json({ error: "Email is required" }, 400)
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400)
    }

    const supabaseClient = createSupabaseClient()
    
    // Use Supabase Auth Admin API generateLink with recovery type as requested
    // This will send an email using the recovery template configured in Supabase
    // The redirect URL will be customized to indicate it's for waitlist acknowledgment
    const { data, error: linkError } = await supabaseClient.auth.admin.generateLink({
      type: 'recovery',
      email: email,
      options: {
        redirectTo: `${process.env.SITE_URL || 'https://hollywoodtable.com'}/waitlist-confirmation`
      }
    })

    if (linkError) {
      logStep("api", "Failed to generate recovery link for waitlist acknowledgment", { 
        error: linkError.message,
        email: email
      })
      return c.json({ 
        error: "Failed to send acknowledgment email",
        details: linkError.message 
      }, 500)
    }

    logStep("api", "Waitlist acknowledgment email sent successfully using recovery template", { 
      email: email,
      actionLinkSent: !!data.properties?.action_link,
      redirectTo: data.properties?.redirect_to
    })

    return c.json({ 
      success: true, 
      message: "Waitlist acknowledgment email sent successfully using recovery template",
      emailSent: true
    })

  } catch (error) {
    logStep("api", "Internal server error in waitlist acknowledgment", { 
      error: (error as Error).message 
    })
    return c.json({ 
      error: "Internal server error",
      details: (error as Error).message 
    }, 500)
  }
})

// ================================
// ADMIN ROUTES (/api/admin/*)
// ================================

app.post("/api/admin/invite-users", async (c) => {
  try {
    const { emails, additionalParams } = await c.req.json()
    
    if (!emails || !Array.isArray(emails)) {
      return c.json({ error: "emails array is required" }, 400)
    }

    const supabaseClient = createSupabaseClient()
    
    const responses = await Promise.all(emails.map(async (email: string) => {
      try {
        let { data: _user } = await supabaseClient
          .from("waitlist")
          .select("*")
          .eq("email", email)
          .single()

        if (!_user) {
          const { error: insertError } = await supabaseClient
            .from("waitlist")
            .insert([{ email, name: '', reason: '' }])
            .select()

          if (insertError) {
            return { email, error: insertError.message }
          }
        }

        const inviteCodes = Array.from({ length: 3 }, () => nanoid())
        const inviteData = { inviteCodes, hasPassword: false }

        const { error: inviteError } = await supabaseClient.auth.admin.inviteUserByEmail(email, {
          data: inviteData
        })

        if (inviteError) {
          return { email, error: inviteError.message }
        }

        const { data: authData, error: userError } = await supabaseClient.auth.admin.listUsers()
        
        if (userError) {
          return { email, error: userError.message }
        }

        const user = authData.users.find(u => u.email === email)
        
        if (!user) {
          return { email, error: "User not found after invitation" }
        }

        const inviteCodeInserts = inviteCodes.map(code => ({
          code,
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          used: false,
          from_user: user.id
        }))

        const { error: insertError } = await supabaseClient
          .from("invite_codes")
          .insert(inviteCodeInserts)

        if (insertError) {
          return { email, error: insertError.message }
        }
        
        return { email, inviteCodes, userId: user.id }

      } catch (error) {
        return { email, error: (error as Error).message }
      }
    }))

    return c.json(responses)

  } catch (error) {
    logStep("api", "Internal server error in admin invite", { error: (error as Error).message })
    return c.json({ error: "Internal server error" }, 500)
  }
})

// ================================
// HEALTH CHECK
// ================================

app.get("/api/health", (c) => {
  return c.json({ 
    status: "ok", 
    service: "consolidated-api",
    features: {
      "auto_subscription_creation": "All routes automatically ensure users have a free subscription record if none exists (ensureUserSubscription function uses service role for all operations)",
      "message_tracking_only": "System now tracks message usage only (conversations are no longer counted toward limits)"
    },
    routes: {
      chat: ["/api/chat"],
      usage: ["/api/usage/check-limits (action_type: 'message' only)"],
      billing: [
        "/api/billing/subscription", 
        "/api/billing/customer-portal", 
        "/api/billing/purchase-credits",
        "/api/billing/manage-subscription (supports action: 'cancel_keep_credits')"
      ],
      waitlist: ["/api/waitlist/send-acknowledgment"],
      admin: ["/api/admin/invite-users"]
    }
  })
})

app.get('/', (c) => {
  return c.text('Welcome to the Hollywood Table Backend API')
})

export default app
