import type { Context, Next } from 'hono';
import { logStep } from '../shared/logger';

/**
 * Environment context interface for request-scoped environment information
 */
export interface EnvironmentContext {
  environment: 'test' | 'production';
  tablePrefix: string;
  isTestEnvironment: boolean;
}

/**
 * Environment detection middleware
 * 
 * Detects the x-env header and sets environment context:
 * - x-env: test -> test environment (uses _test_ prefixed tables)
 * - x-env: live -> production environment (uses normal tables)
 * - no header -> production environment (default)
 * 
 * The environment context is stored in the Hono context and can be accessed
 * by subsequent middleware and route handlers.
 */
export const environmentMiddleware = async (c: Context, next: Next) => {
  const envHeader = c.req.header('x-env');
  
  // Determine environment based on header
  const environment = envHeader === 'test' ? 'test' : 'production';
  const tablePrefix = environment === 'test' ? '_test_' : '';
  const isTestEnvironment = environment === 'test';
  
  // Create environment context
  const environmentContext: EnvironmentContext = {
    environment,
    tablePrefix,
    isTestEnvironment
  };
  
  // Store in Hono context for access by other middleware and handlers
  c.set('environmentContext', environmentContext);
  
  // Log environment detection for debugging
  logStep('middleware', 'Environment detected', {
    environment,
    tablePrefix,
    isTestEnvironment,
    headerValue: envHeader || 'not-provided'
  });
  
  await next();
};

/**
 * Helper function to get environment context from Hono context
 * 
 * @param c - Hono context
 * @returns Environment context or default production context
 */
export const getEnvironmentContext = (c: Context): EnvironmentContext => {
  const context = c.get('environmentContext') as EnvironmentContext;
  
  // Return default production context if not set (fallback)
  if (!context) {
    logStep('middleware', 'Environment context not found, defaulting to production', {});
    return {
      environment: 'production',
      tablePrefix: '',
      isTestEnvironment: false
    };
  }
  
  return context;
};

/**
 * Helper function to check if current request is in test environment
 * 
 * @param c - Hono context
 * @returns true if in test environment, false otherwise
 */
export const isTestEnvironment = (c: Context): boolean => {
  const context = getEnvironmentContext(c);
  return context.isTestEnvironment;
};

/**
 * Helper function to get the appropriate table name for the current environment
 * 
 * @param baseTableName - The base table name (e.g., 'users', 'messages')
 * @param c - Hono context
 * @returns The environment-appropriate table name
 */
export const getTableName = (baseTableName: string, c: Context): string => {
  const context = getEnvironmentContext(c);
  return context.isTestEnvironment ? `${context.tablePrefix}${baseTableName}` : baseTableName;
};

/**
 * Type guard to check if environment context is properly set
 * 
 * @param context - Potential environment context
 * @returns true if context is valid EnvironmentContext
 */
export const isValidEnvironmentContext = (context: any): context is EnvironmentContext => {
  return (
    context &&
    typeof context === 'object' &&
    typeof context.environment === 'string' &&
    (context.environment === 'test' || context.environment === 'production') &&
    typeof context.tablePrefix === 'string' &&
    typeof context.isTestEnvironment === 'boolean'
  );
};

/**
 * Middleware to validate environment context is properly set
 * Use this after environmentMiddleware to ensure context is available
 */
export const validateEnvironmentMiddleware = async (c: Context, next: Next) => {
  const context = c.get('environmentContext');
  
  if (!isValidEnvironmentContext(context)) {
    logStep('middleware', 'Invalid or missing environment context', { context });
    return c.json({ 
      error: 'Environment context not properly initialized',
      code: 'ENVIRONMENT_ERROR'
    }, 500);
  }
  
  await next();
};

/**
 * Constants for environment values
 */
export const ENVIRONMENT_VALUES = {
  TEST: 'test' as const,
  PRODUCTION: 'production' as const,
  LIVE: 'live' as const
} as const;

/**
 * Constants for header names
 */
export const ENVIRONMENT_HEADERS = {
  X_ENV: 'x-env' as const
} as const;

/**
 * Default table prefix for test environment
 */
export const DEFAULT_TEST_PREFIX = '_test_' as const;

/**
 * Export types for use in other modules
 */
export type Environment = 'test' | 'production';
export type EnvironmentHeader = 'test' | 'live';
