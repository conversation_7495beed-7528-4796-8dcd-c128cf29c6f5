import { Hono } from 'hono'
import { createSupabaseClient, createSupabaseClientFromContext, getAuthenticatedUser, ensureUserSubscription } from '../shared/supabase'
import { createEnvironmentAwareSupabaseClientFromContext } from '../shared/supabase-enhanced'
import { logStep } from '../shared/logger'
import { createStripeClient } from '../shared/stripe'
import { getStripePriceId, PAYG_PACKAGES } from '../shared/pricing'
import {
  pauseSubscriptionHandler,
  resumeSubscriptionHandler,
  cancelAtPeriodEndHandler,
  reactivateSubscriptionHandler,
  previewSubscriptionChangeHandler,
  getSubscriptionHistoryHandler
} from './billing/subscription-management.handlers'
import {
  getTrialInfoHandler,
  extendTrialHandler,
  startTrialHandler,
  cancelTrialHandler
} from './billing/trial-management.handlers'
import {
  getDunningStatusHandler,
  retryPaymentHandler,
  updatePaymentMethodHandler,
  getPaymentFailureHistoryHandler,
  sendDunningNotificationHandler,
  getDunningMetricsHandler
} from './billing/dunning-management.handlers'
import {
  getSubscriptionMetricsHandler,
  getSubscriptionOverviewHandler,
  getUserSubscriptionAnalyticsHandler,
  calculateSubscriptionMetricsHandler
} from './billing/analytics.handlers'

const billing = new Hono()

// ================================
// BILLING ROUTES (/billing/*)
// ================================

billing.get("/subscription", async (c) => {
  try {
    logStep("api", "Starting check-subscription")

    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401)
    }

    const supabaseClient = createEnvironmentAwareSupabaseClientFromContext(c)

    // Ensure test tables exist if in test environment
    if (supabaseClient.environment.isTestEnvironment) {
      await supabaseClient.ensureTestTable('user_subscriptions');
    }

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id)
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in billing/subscription", { error: (subscriptionError as Error).message })
      return c.json({
        error: "Unable to initialize user subscription. Please try again.",
        details: (subscriptionError as Error).message
      }, 500)
    }

    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(`
        subscription_tier,
        subscription_status,
        subscription_start_date,
        subscription_end_date,
        stripe_subscription_id,
        stripe_customer_id,
        messages_used_this_period,
        monthly_message_limit,
        payg_credits_remaining,
        current_period_start,
        current_period_end
      `)
      .eq("user_id", user.id)
      .single()

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation in billing/subscription", { userId: user.id })
      return c.json({ error: "Subscription record not found" }, 500)
    }

    // Check paid tier status
    const isPaidTier = subscription.subscription_tier !== "Sample the Table"
    const isActiveStatus = subscription.subscription_status === "active"
    const hasValidEndDate = subscription.subscription_end_date && 
      new Date(subscription.subscription_end_date) > new Date()

    let subscribed = false
    let subscriptionEnd: string | null = null

    if (isPaidTier && subscription.stripe_subscription_id) {
      if (isActiveStatus && hasValidEndDate) {
        subscribed = true
        subscriptionEnd = subscription.subscription_end_date
      } else {
        try {
          const stripe = createStripeClient()
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripe_subscription_id
          )

          subscribed = stripeSubscription.status === "active"
          subscriptionEnd = subscribed ? 
            new Date((stripeSubscription as any).current_period_end * 1000).toISOString() : 
            null

          // Update database with fresh data
          if (subscribed !== isActiveStatus || subscriptionEnd !== subscription.subscription_end_date) {
            await supabaseClient
              .from("user_subscriptions")
              .update({
                subscription_status: stripeSubscription.status,
                subscription_end_date: subscriptionEnd,
                updated_at: new Date().toISOString()
              })
              .eq("user_id", user.id)
          }
        } catch (stripeError) {
          logStep("api", "Stripe verification failed", { error: stripeError })
          subscribed = isActiveStatus && hasValidEndDate
          subscriptionEnd = subscribed ? subscription.subscription_end_date : null
        }
      }
    }

    return c.json({
      subscribed,
      subscription_tier: subscription.subscription_tier,
      subscription_end: subscriptionEnd,
      usage: {
        messages_used: subscription.messages_used_this_period || 0,
        messages_limit: subscription.monthly_message_limit || 0,
        payg_credits_remaining: subscription.payg_credits_remaining || 0
      }
    })

  } catch (error) {
    logStep("api", "Internal server error in billing subscription", { error: (error as Error).message })
    return c.json({ error: "Internal server error" }, 500)
  }
})

billing.post("/customer-portal", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401)
    }

    const stripe = createStripeClient()
    const customers = await stripe.customers.list({ email: user.email, limit: 1 })
    
    if (customers.data.length === 0) {
      return c.json({ error: "No Stripe customer found for this user" }, 400)
    }
    
    const customerId = customers.data[0].id
    const origin = c.req.header("origin") || "http://localhost:3000"
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${origin}/account`,
    })

    return c.json({ url: portalSession.url })

  } catch (error) {
    logStep("api", "Error in customer-portal", { error: (error as Error).message })
    return c.json({ error: (error as Error).message }, 500)
  }
})

billing.post("/purchase-credits", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401)
    }

    const { package_type } = await c.req.json()
    
    if (!package_type || !PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES]) {
      return c.json({ 
        error: 'Invalid package_type. Must be "starter", "writers", or "pro"' 
      }, 400)
    }

    const selectedPackage = PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES]
    const supabaseClient = createSupabaseClientFromContext(c)

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id)
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in purchase-credits", { error: (subscriptionError as Error).message })
      return c.json({
        error: "Unable to initialize user subscription. Please try again.",
        details: (subscriptionError as Error).message
      }, 500)
    }

    // Get subscription record
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single()

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation in purchase-credits", { userId: user.id })
      return c.json({ error: "Subscription record not found" }, 500)
    }

    // Create or get Stripe customer
    let customerId = subscription.stripe_customer_id
    
    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single()

      const stripe = createStripeClient()
      const customer = await stripe.customers.create({
        email: profile?.email || user.email,
        name: profile?.full_name || undefined,
        metadata: { supabase_user_id: user.id },
      })
      
      customerId = customer.id
      
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id)
    }

    // Create Stripe Checkout session for one-time payment
    const stripe = createStripeClient()
    const baseUrl = process.env.NODE_ENV === "production"
      ? "https://www.hollywoodtable.com"
      : "http://localhost:8080"

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'usd',
          product_data: {
            name: `${selectedPackage.name} Credit Package`,
            description: selectedPackage.description,
          },
          unit_amount: selectedPackage.price_cents,
        },
        quantity: 1,
      }],
      mode: 'payment',
      success_url: `${baseUrl}/pricing?payg_success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${baseUrl}/pricing?payg_canceled=true`,
      metadata: {
        user_id: user.id,
        package_type: package_type,
        credits: selectedPackage.credits.toString(),
        type: "payg_credits"
      },
      billing_address_collection: 'auto',
      customer_update: { address: 'auto', name: 'auto' }
    })

    return c.json({
      success: true,
      checkout_url: session.url,
      session_id: session.id,
      package_info: {
        type: package_type,
        credits: selectedPackage.credits,
        price_cents: selectedPackage.price_cents,
        name: selectedPackage.name
      }
    })

  } catch (error) {
    logStep("api", "Error in purchase-credits", { error: (error as Error).message })
    return c.json({ error: "Internal server error" }, 500)
  }
})

billing.post("/manage-subscription", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c)
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401)
    }

    const { 
      target_tier: targetTier, 
      billing_cycle: rawCycle, 
      action 
    } = await c.req.json()
    const billingCycle = rawCycle === 'annual' ? 'annual' : 'monthly'

    // Handle cancel_keep_credits action
    if (action === 'cancel_keep_credits') {
      logStep("api", "Processing cancel subscription (move to free tier, keep usage)", { userId: user.id })

      const supabaseClient = createSupabaseClient()

      // Fetch subscription record first to get stripe_subscription_id
      const { data: subscription, error: subError } = await supabaseClient
        .from("user_subscriptions")
        .select("*")
        .eq("user_id", user.id)
        .single()

      if (subError || !subscription) {
        return c.json({ error: "No active subscription found" }, 404)
      }

      if (subscription.subscription_tier === 'Sample the Table') {
        return c.json({ error: "Already on free tier" }, 400)
      }

      const _stripe = createStripeClient()

      // Cancel the Stripe subscription at period end if it exists
      if (subscription.stripe_subscription_id) {
        await _stripe.subscriptions.update(subscription.stripe_subscription_id, {
          cancel_at_period_end: true
        })

        logStep("api", "Stripe subscription set to cancel at period end", { 
          subscriptionId: subscription.stripe_subscription_id 
        })
      }

      // Use the new database function to move to free tier while keeping usage
      const { data: cancelResult, error: cancelError } = await supabaseClient
        .rpc('cancel_subscription_to_free_tier', {
          user_uuid: user.id
        })

      if (cancelError) {
        logStep("api", "Error cancelling subscription", { error: cancelError })
        return c.json({ 
          error: "Failed to cancel subscription", 
          details: cancelError.message 
        }, 500)
      }

      const result = cancelResult[0]
      if (!result.success) {
        return c.json({ error: result.message }, 400)
      }

      return c.json({
        success: true,
        message: "Subscription cancelled. You've been moved to the free tier but will keep your current usage until your next billing cycle.",
        previous_tier: result.previous_tier,
        current_tier: result.new_tier,
        effective_date: subscription.subscription_end_date,
        usage_kept: result.usage_kept,
        action: 'cancel_keep_credits'
      })
    }

    // Validate target tier for tier change operations
    const SUBSCRIPTION_TIERS = ["Sample the Table", "Reserved Seat", "VIP Table"] as const
    if (!targetTier || !SUBSCRIPTION_TIERS.includes(targetTier)) {
      return c.json({ 
        error: `Invalid target_tier. Must be one of ${SUBSCRIPTION_TIERS.join(', ')}` 
      }, 400)
    }

    logStep("api", "Processing subscription change", { 
      userId: user.id, 
      targetTier, 
      billingCycle 
    })

    const supabaseClient = createSupabaseClient()

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id)
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in manage-subscription", { error: (subscriptionError as Error).message })
      return c.json({
        error: "Unable to initialize user subscription. Please try again.",
        details: (subscriptionError as Error).message
      }, 500)
    }

    // Fetch subscription record 
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single()

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation", { userId: user.id })
      return c.json({ error: "Subscription record not found" }, 500)
    }

    const currentTier = subscription.subscription_tier
    const currentIndex = SUBSCRIPTION_TIERS.indexOf(currentTier as any)
    const targetIndex = SUBSCRIPTION_TIERS.indexOf(targetTier)

    if (currentTier === targetTier) {
      return c.json({ error: "Already on requested tier" }, 400)
    }

    // Resolve Stripe customer
    let customerId = subscription.stripe_customer_id
    const _stripe = createStripeClient()

    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single()

      const customer = await _stripe.customers.create({
        email: profile?.email || user.email,
        name: profile?.full_name,
        metadata: { supabase_user_id: user.id }
      })
      
      customerId = customer.id
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id)
    }

    // Determine upgrade or downgrade
    if (targetIndex > currentIndex) {
      // UPGRADE LOGIC
      logStep("api", "Processing upgrade", { 
        userId: user.id, 
        from: currentTier, 
        to: targetTier 
      })

      const priceId = getStripePriceId(targetTier, billingCycle)
      if (!priceId) {
        return c.json({ error: "Price ID missing for upgrade" }, 500)
      }

      if (subscription.stripe_subscription_id) {
        // Modify existing subscription
        const stripeSubscription = await _stripe.subscriptions.retrieve(subscription.stripe_subscription_id)
        const updatedSubscription = await _stripe.subscriptions.update(subscription.stripe_subscription_id, {
          items: [{ id: stripeSubscription.items.data[0].id, price: priceId }],
          proration_behavior: 'create_prorations'
        })

        logStep("api", "Updated subscription", { updatedSubscription })

        await supabaseClient.rpc('update_subscription_tier_limits', {
          user_uuid: user.id,
          new_tier: targetTier
        })

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            billing_cycle: billingCycle,
            subscription_end_date: new Date((updatedSubscription as any).current_period_end * 1000).toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq("user_id", user.id)

        return c.json({
          success: true,
          message: "Upgraded successfully",
          new_tier: targetTier,
          billing_cycle: billingCycle
        })
      } else {
        // Create checkout session for new subscription
        const baseUrl = process.env.NODE_ENV === "production"
          ? "https://www.hollywoodtable.com"
          : "http://localhost:8080"

        const session = await _stripe.checkout.sessions.create({
          customer: customerId,
          payment_method_types: ['card'],
          line_items: [{ price: priceId, quantity: 1 }],
          mode: 'subscription',
          success_url: `${baseUrl}/pricing?session_id={CHECKOUT_SESSION_ID}&success=true`,
          cancel_url: `${baseUrl}/pricing?canceled=true`,
          metadata: {
            supabase_user_id: user.id,
            subscription_tier: targetTier,
            billing_cycle: billingCycle
          },
          allow_promotion_codes: true,
          billing_address_collection: 'auto',
          customer_update: { address: 'auto', name: 'auto' }
        })

        return c.json({
          success: true,
          checkout_url: session.url,
          session_id: session.id,
          new_tier: targetTier,
          billing_cycle: billingCycle
        })
      }
    } else {
      // DOWNGRADE LOGIC
      logStep("api", "Processing downgrade", { 
        userId: user.id, 
        from: currentTier, 
        to: targetTier 
      })

      if (targetTier === 'Sample the Table') {
        // Downgrade to free tier
        if (subscription.stripe_subscription_id) {
          await _stripe.subscriptions.update(subscription.stripe_subscription_id, {
            cancel_at_period_end: true
          })
        }

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            subscription_status: 'canceled',
            updated_at: new Date().toISOString()
          })
          .eq("user_id", user.id)

        return c.json({
          success: true,
          message: "Subscription will be canceled at period end",
          new_tier: targetTier,
          effective_date: subscription.subscription_end_date
        })
      } else {
        // Downgrade between paid tiers
        if (!subscription.stripe_subscription_id) {
          // No active Stripe subscription, just update database
          await supabaseClient
            .from("user_subscriptions")
            .update({
              subscription_tier: targetTier,
              subscription_status: 'active',
              updated_at: new Date().toISOString()
            })
            .eq("user_id", user.id)

          return c.json({
            success: true,
            message: `Downgraded to ${targetTier}`,
            new_tier: targetTier
          })
        }

        const newPriceId = getStripePriceId(targetTier, billingCycle)
        if (!newPriceId) {
          return c.json({ error: "Price ID missing for downgrade" }, 500)
        }

        const stripeSubscription = await _stripe.subscriptions.retrieve(subscription.stripe_subscription_id)
        const updatedSubscription = await _stripe.subscriptions.update(subscription.stripe_subscription_id, {
          items: [{ id: stripeSubscription.items.data[0].id, price: newPriceId }],
          proration_behavior: 'create_prorations'
        })

        await supabaseClient.rpc('update_subscription_tier_limits', {
          user_uuid: user.id,
          new_tier: targetTier
        })

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            billing_cycle: billingCycle,
            subscription_end_date: new Date((updatedSubscription as any).current_period_end * 1000).toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq("user_id", user.id)

        return c.json({
          success: true,
          message: "Downgraded successfully",
          new_tier: targetTier,
          billing_cycle: billingCycle,
          proration_credit: "A credit has been applied to your account for the difference"
        })
      }
    }

  } catch (error) {
    logStep("api", "Error in manage-subscription", { error: (error as Error).message })
    return c.json({
      error: "Internal server error",
      details: (error as Error).message
    }, 500)
  }
})

// ================================
// ENHANCED SUBSCRIPTION MANAGEMENT ROUTES
// ================================

// Pause subscription
billing.post("/subscription/pause", pauseSubscriptionHandler)

// Resume subscription
billing.post("/subscription/resume", resumeSubscriptionHandler)

// Cancel subscription (at period end or immediately)
billing.post("/subscription/cancel", cancelAtPeriodEndHandler)

// Reactivate canceled subscription
billing.post("/subscription/reactivate", reactivateSubscriptionHandler)

// Preview subscription change
billing.post("/subscription/preview-change", previewSubscriptionChangeHandler)

// Get subscription history
billing.get("/subscription/history", getSubscriptionHistoryHandler)

// ================================
// TRIAL MANAGEMENT ROUTES
// ================================

// Get trial information
billing.get("/trial/info", getTrialInfoHandler)

// Extend trial period
billing.post("/trial/extend", extendTrialHandler)

// Start trial for free tier users
billing.post("/trial/start", startTrialHandler)

// Cancel trial
billing.post("/trial/cancel", cancelTrialHandler)

// ================================
// DUNNING MANAGEMENT ROUTES
// ================================

// Get dunning status
billing.get("/dunning/status", getDunningStatusHandler)

// Retry failed payment
billing.post("/dunning/retry-payment", retryPaymentHandler)

// Update payment method
billing.post("/dunning/update-payment-method", updatePaymentMethodHandler)

// Get payment failure history
billing.get("/dunning/failure-history", getPaymentFailureHistoryHandler)

// Send dunning notification
billing.post("/dunning/send-notification", sendDunningNotificationHandler)

// Get dunning metrics (admin)
billing.get("/dunning/metrics", getDunningMetricsHandler)

// ================================
// ANALYTICS AND REPORTING ROUTES
// ================================

// Get subscription metrics
billing.get("/analytics/metrics", getSubscriptionMetricsHandler)

// Get subscription overview
billing.get("/analytics/overview", getSubscriptionOverviewHandler)

// Get user subscription analytics
billing.get("/analytics/user", getUserSubscriptionAnalyticsHandler)

// Calculate subscription metrics (admin)
billing.post("/analytics/calculate", calculateSubscriptionMetricsHandler)

export default billing