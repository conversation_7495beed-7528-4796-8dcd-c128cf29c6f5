// Analytics and reporting handlers
// Provides subscription metrics and business intelligence

import type { AppRouteHandler } from "../../shared/types";
import { createSupabaseClientFromContext, getAuthenticatedUser } from "../../shared/supabase";
import { logStep } from "../../shared/logger";
import { ERROR_CODES, ERROR_MESSAGES, createErrorResponse, createSuccessResponse } from "../../shared/constants";

// Get subscription metrics handler
export const getSubscriptionMetricsHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { start_date, end_date } = await c.req.json();
    
    logStep("api", "Getting subscription metrics", { userId: user.id, start_date, end_date });
    
    if (!start_date || !end_date) {
      return c.json(createErrorResponse("Start date and end date are required"), 400);
    }
    
    // Get subscription metrics from database
    const { data: metrics, error: metricsError } = await supabaseClient
      .from("subscription_metrics")
      .select("*")
      .gte("period_start", start_date)
      .lte("period_end", end_date)
      .order("period_start", { ascending: false })
      .limit(1)
      .single();
    
    if (metricsError && metricsError.code !== 'PGRST116') {
      logStep("api", "Error fetching subscription metrics", { error: metricsError.message });
      return c.json(createErrorResponse("Failed to fetch metrics"), 500);
    }
    
    // If no metrics found, calculate basic metrics
    if (!metrics) {
      const { data: subscriptions, error: subError } = await supabaseClient
        .from("user_subscriptions")
        .select(`
          subscription_tier,
          subscription_status,
          created_at,
          updated_at
        `);
      
      if (subError) {
        return c.json(createErrorResponse("Failed to calculate metrics"), 500);
      }
      
      // Calculate basic metrics
      const totalSubscriptions = subscriptions?.filter(s => s.subscription_status === 'active').length || 0;
      const paidSubscriptions = subscriptions?.filter(s => 
        s.subscription_tier !== 'Sample the Table' && s.subscription_status === 'active'
      ).length || 0;
      
      // Estimate MRR (this would need actual pricing data)
      const estimatedMRR = paidSubscriptions * 1000; // $10 average per subscription in cents
      
      return c.json(createSuccessResponse({
        totalSubscriptions,
        newSubscriptions: 0,
        canceledSubscriptions: 0,
        upgradedSubscriptions: 0,
        downgradeSubscriptions: 0,
        mrrCents: estimatedMRR,
        arrCents: estimatedMRR * 12,
        totalRevenueCents: estimatedMRR,
        churnRate: 0,
        retentionRate: 100,
        trialConversions: 0,
        trialStarts: 0
      }));
    }
    
    return c.json(createSuccessResponse(metrics));
    
  } catch (error) {
    logStep("api", "Error getting subscription metrics", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Get subscription overview handler
export const getSubscriptionOverviewHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Getting subscription overview", { userId: user.id });
    
    // Get subscription tier distribution
    const { data: tierDistribution, error: tierError } = await supabaseClient
      .from("user_subscriptions")
      .select("subscription_tier, subscription_status")
      .eq("subscription_status", "active");
    
    if (tierError) {
      return c.json(createErrorResponse("Failed to fetch tier distribution"), 500);
    }
    
    // Calculate tier distribution
    const tiers = tierDistribution?.reduce((acc, sub) => {
      acc[sub.subscription_tier] = (acc[sub.subscription_tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};
    
    // Get recent subscription changes
    const { data: recentChanges, error: changesError } = await supabaseClient
      .from("subscription_change_history")
      .select(`
        change_type,
        from_tier,
        to_tier,
        created_at
      `)
      .gte("created_at", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order("created_at", { ascending: false })
      .limit(10);
    
    if (changesError) {
      return c.json(createErrorResponse("Failed to fetch recent changes"), 500);
    }
    
    // Get payment failure summary
    const { data: paymentFailures, error: failuresError } = await supabaseClient
      .from("payment_failures")
      .select("resolved_at")
      .gte("created_at", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
    
    if (failuresError) {
      return c.json(createErrorResponse("Failed to fetch payment failures"), 500);
    }
    
    const totalFailures = paymentFailures?.length || 0;
    const resolvedFailures = paymentFailures?.filter(f => f.resolved_at).length || 0;
    const recoveryRate = totalFailures > 0 ? (resolvedFailures / totalFailures) * 100 : 0;
    
    return c.json(createSuccessResponse({
      tierDistribution: tiers,
      recentChanges: recentChanges || [],
      paymentHealth: {
        totalFailures,
        resolvedFailures,
        recoveryRate
      },
      totalActiveSubscriptions: tierDistribution?.length || 0
    }));
    
  } catch (error) {
    logStep("api", "Error getting subscription overview", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Get user subscription analytics handler
export const getUserSubscriptionAnalyticsHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Getting user subscription analytics", { userId: user.id });
    
    // Get user's subscription history
    const { data: userHistory, error: historyError } = await supabaseClient
      .from("subscription_change_history")
      .select(`
        change_type,
        from_tier,
        to_tier,
        proration_amount_cents,
        effective_date,
        created_at
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });
    
    if (historyError) {
      return c.json(createErrorResponse("Failed to fetch user history"), 500);
    }
    
    // Get user's current subscription
    const { data: currentSub, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(`
        subscription_tier,
        subscription_status,
        subscription_start_date,
        messages_used_this_period,
        monthly_message_limit,
        payg_credits_remaining,
        payg_total_spent_cents
      `)
      .eq("user_id", user.id)
      .single();
    
    if (subError) {
      return c.json(createErrorResponse("Failed to fetch current subscription"), 500);
    }
    
    // Calculate user metrics
    const totalSpent = (userHistory || [])
      .filter(h => h.proration_amount_cents > 0)
      .reduce((sum, h) => sum + h.proration_amount_cents, 0);
    
    const subscriptionDuration = currentSub?.subscription_start_date 
      ? Math.floor((Date.now() - new Date(currentSub.subscription_start_date).getTime()) / (1000 * 60 * 60 * 24))
      : 0;
    
    const usagePercentage = currentSub?.monthly_message_limit 
      ? (currentSub.messages_used_this_period / currentSub.monthly_message_limit) * 100
      : 0;
    
    return c.json(createSuccessResponse({
      currentSubscription: currentSub,
      subscriptionHistory: userHistory || [],
      analytics: {
        totalSpentCents: totalSpent + (currentSub?.payg_total_spent_cents || 0),
        subscriptionDurationDays: subscriptionDuration,
        usagePercentage,
        totalChanges: userHistory?.length || 0,
        upgrades: userHistory?.filter(h => h.change_type === 'upgrade').length || 0,
        downgrades: userHistory?.filter(h => h.change_type === 'downgrade').length || 0
      }
    }));
    
  } catch (error) {
    logStep("api", "Error getting user subscription analytics", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Calculate and update subscription metrics (admin function)
export const calculateSubscriptionMetricsHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { period_start, period_end } = await c.req.json();
    
    logStep("api", "Calculating subscription metrics", { userId: user.id, period_start, period_end });
    
    if (!period_start || !period_end) {
      return c.json(createErrorResponse("Period start and end dates are required"), 400);
    }
    
    const startDate = new Date(period_start);
    const endDate = new Date(period_end);
    
    // Get subscription changes in the period
    const { data: changes, error: changesError } = await supabaseClient
      .from("subscription_change_history")
      .select("*")
      .gte("effective_date", startDate.toISOString())
      .lte("effective_date", endDate.toISOString());
    
    if (changesError) {
      return c.json(createErrorResponse("Failed to fetch subscription changes"), 500);
    }
    
    // Get current active subscriptions
    const { data: activeSubscriptions, error: activeError } = await supabaseClient
      .from("user_subscriptions")
      .select("subscription_tier, subscription_status")
      .eq("subscription_status", "active");
    
    if (activeError) {
      return c.json(createErrorResponse("Failed to fetch active subscriptions"), 500);
    }
    
    // Calculate metrics
    const newSubscriptions = changes?.filter(c => c.change_type === 'upgrade' && c.from_tier === 'Sample the Table').length || 0;
    const canceledSubscriptions = changes?.filter(c => c.change_type === 'cancel' || c.change_type === 'downgrade').length || 0;
    const upgradedSubscriptions = changes?.filter(c => c.change_type === 'upgrade' && c.from_tier !== 'Sample the Table').length || 0;
    const downgradeSubscriptions = changes?.filter(c => c.change_type === 'downgrade' && c.to_tier !== 'Sample the Table').length || 0;
    
    const totalSubscriptions = activeSubscriptions?.length || 0;
    const paidSubscriptions = activeSubscriptions?.filter(s => s.subscription_tier !== 'Sample the Table').length || 0;
    
    // Estimate MRR (this would need actual pricing data)
    const estimatedMRR = paidSubscriptions * 1000; // $10 average per subscription in cents
    
    const churnRate = totalSubscriptions > 0 ? (canceledSubscriptions / totalSubscriptions) * 100 : 0;
    const retentionRate = 100 - churnRate;
    
    // Insert or update metrics
    const metricsData = {
      period_start: startDate.toISOString().split('T')[0],
      period_end: endDate.toISOString().split('T')[0],
      total_subscriptions: totalSubscriptions,
      new_subscriptions: newSubscriptions,
      canceled_subscriptions: canceledSubscriptions,
      upgraded_subscriptions: upgradedSubscriptions,
      downgraded_subscriptions: downgradeSubscriptions,
      mrr_cents: estimatedMRR,
      arr_cents: estimatedMRR * 12,
      total_revenue_cents: estimatedMRR,
      churn_rate: churnRate,
      retention_rate: retentionRate,
      trial_conversions: 0, // Would need trial data
      trial_starts: 0
    };
    
    const { data: metrics, error: metricsError } = await supabaseClient
      .from("subscription_metrics")
      .upsert(metricsData, { onConflict: 'period_start,period_end' })
      .select()
      .single();
    
    if (metricsError) {
      return c.json(createErrorResponse("Failed to save metrics"), 500);
    }
    
    return c.json(createSuccessResponse({
      message: "Metrics calculated and saved successfully",
      metrics
    }));
    
  } catch (error) {
    logStep("api", "Error calculating subscription metrics", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};
