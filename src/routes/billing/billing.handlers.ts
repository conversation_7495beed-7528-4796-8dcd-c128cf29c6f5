// Billing request handlers following the reference pattern
// Clean separation of business logic from route definitions

import type { AppRouteHandler } from "../../shared/types";
import { createSupabaseClientFromContext, getAuthenticatedUser, ensureUserSubscription } from "../../shared/supabase";
import { logStep } from "../../shared/logger";
import { createStripeClient } from "../../shared/stripe";
import { getStripePriceId, getPaygPriceId, PAYG_PACKAGES } from "../../shared/pricing";
import { ERROR_CODES, ERROR_MESSAGES, createErrorResponse, createSuccessResponse } from "../../shared/constants";

import type { 
  GetSubscriptionRoute, 
  CreateCustomerPortalRoute, 
  PurchaseCreditsRoute, 
  CreateCheckoutSessionRoute 
} from "./billing.routes";

export const getSubscription: AppRouteHandler = async (c) => {
  try {
    logStep("api", "Starting check-subscription");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(createErrorResponse(ERROR_MESSAGES.UNAUTHORIZED, ERROR_CODES.UNAUTHORIZED), 401);
    }

    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in billing/subscription", { 
        error: (subscriptionError as Error).message 
      });
      return c.json(createErrorResponse(
        "Unable to initialize user subscription. Please try again.",
        ERROR_CODES.INTERNAL_SERVER_ERROR
      ), 500);
    }

    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(`
        subscription_tier,
        subscription_status,
        subscription_start_date,
        subscription_end_date,
        stripe_subscription_id,
        stripe_customer_id,
        messages_used_this_period,
        monthly_message_limit,
        payg_credits_remaining,
        current_period_start,
        current_period_end
      `)
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation in billing/subscription", { userId: user.id });
      return c.json(createErrorResponse("Subscription record not found"), 500);
    }

    // Check paid tier status
    const isPaidTier = subscription.subscription_tier !== "Sample the Table";
    const isActiveStatus = subscription.subscription_status === "active";
    const hasValidEndDate = subscription.subscription_end_date && 
      new Date(subscription.subscription_end_date) > new Date();

    let subscribed = false;
    let subscriptionEnd: string | null = null;

    if (isPaidTier && subscription.stripe_subscription_id) {
      if (isActiveStatus && hasValidEndDate) {
        subscribed = true;
        subscriptionEnd = subscription.subscription_end_date;
      } else {
        try {
          const stripe = createStripeClient();
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripe_subscription_id
          );

          subscribed = stripeSubscription.status === "active";
          subscriptionEnd = subscribed ? 
            new Date((stripeSubscription as any).current_period_end * 1000).toISOString() : 
            null;

          // Update database with fresh data
          if (subscribed !== isActiveStatus || subscriptionEnd !== subscription.subscription_end_date) {
            await supabaseClient
              .from("user_subscriptions")
              .update({
                subscription_status: stripeSubscription.status,
                subscription_end_date: subscriptionEnd,
                updated_at: new Date().toISOString()
              })
              .eq("user_id", user.id);
          }
        } catch (stripeError) {
          logStep("api", "Stripe verification failed", { error: stripeError });
          subscribed = isActiveStatus && hasValidEndDate;
          subscriptionEnd = subscribed ? subscription.subscription_end_date : null;
        }
      }
    }

    const responseData = {
      subscribed,
      subscription_tier: subscription.subscription_tier,
      subscription_end: subscriptionEnd,
      usage: {
        messages_used: subscription.messages_used_this_period || 0,
        messages_limit: subscription.monthly_message_limit || 0,
        payg_credits_remaining: subscription.payg_credits_remaining || 0
      }
    };

    return c.json(createSuccessResponse(responseData));

  } catch (error) {
    logStep("api", "Internal server error in billing subscription", { 
      error: (error as Error).message 
    });
    return c.json(createErrorResponse(ERROR_MESSAGES.INTERNAL_SERVER_ERROR), 500);
  }
};

export const createCustomerPortal: AppRouteHandler = async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(createErrorResponse(ERROR_MESSAGES.UNAUTHORIZED, ERROR_CODES.UNAUTHORIZED), 401);
    }

    const stripe = createStripeClient();
    const customers = await stripe.customers.list({ email: user.email, limit: 1 });
    
    if (customers.data.length === 0) {
      return c.json(createErrorResponse("No Stripe customer found for this user"), 400);
    }
    
    const customerId = customers.data[0].id;
    const origin = c.req.header("origin") || "http://localhost:3000";
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${origin}/account`,
    });

    return c.json(createSuccessResponse({ url: portalSession.url }));

  } catch (error) {
    logStep("api", "Error in customer-portal", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

export const purchaseCredits: AppRouteHandler = async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(createErrorResponse(ERROR_MESSAGES.UNAUTHORIZED, ERROR_CODES.UNAUTHORIZED), 401);
    }

    const { package_type } = await c.req.json();
    
    if (!package_type || !PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES]) {
      return c.json(createErrorResponse(
        'Invalid package_type. Must be "starter", "writers", or "pro"',
        ERROR_CODES.VALIDATION_ERROR
      ), 400);
    }

    const selectedPackage = PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES];
    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in purchase-credits", { 
        error: (subscriptionError as Error).message 
      });
      return c.json(createErrorResponse(
        "Unable to initialize user subscription. Please try again.",
        ERROR_CODES.INTERNAL_SERVER_ERROR
      ), 500);
    }

    // Get subscription record
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation in purchase-credits", { userId: user.id });
      return c.json(createErrorResponse("Subscription record not found"), 500);
    }

    // Create or get Stripe customer
    let customerId = subscription.stripe_customer_id;
    
    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single();

      const stripe = createStripeClient();
      const customer = await stripe.customers.create({
        email: user.email,
        name: profile?.full_name || undefined,
        metadata: {
          user_id: user.id,
        },
      });

      customerId = customer.id;

      // Update subscription with customer ID
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id);
    }

    // Create Stripe checkout session
    const stripe = createStripeClient();
    const origin = c.req.header("origin") || "http://localhost:3000";
    
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price: getPaygPriceId(package_type),
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${origin}/account?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/account`,
      metadata: {
        user_id: user.id,
        package_type: package_type,
        credits: selectedPackage.credits.toString(),
      },
    });

    return c.json(createSuccessResponse({ checkout_url: session.url }));

  } catch (error) {
    logStep("api", "Error in purchase-credits", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

export const createCheckoutSession: AppRouteHandler = async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(createErrorResponse(ERROR_MESSAGES.UNAUTHORIZED, ERROR_CODES.UNAUTHORIZED), 401);
    }

    const { tier } = await c.req.json();
    
    if (!tier || !["Screenwriter", "Director", "Producer"].includes(tier)) {
      return c.json(createErrorResponse(
        'Invalid tier. Must be "Screenwriter", "Director", or "Producer"',
        ERROR_CODES.VALIDATION_ERROR
      ), 400);
    }

    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in create-checkout-session", { 
        error: (subscriptionError as Error).message 
      });
      return c.json(createErrorResponse(
        "Unable to initialize user subscription. Please try again.",
        ERROR_CODES.INTERNAL_SERVER_ERROR
      ), 500);
    }

    // Get subscription record
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation in create-checkout-session", { userId: user.id });
      return c.json(createErrorResponse("Subscription record not found"), 500);
    }

    // Create or get Stripe customer
    let customerId = subscription.stripe_customer_id;
    
    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single();

      const stripe = createStripeClient();
      const customer = await stripe.customers.create({
        email: user.email,
        name: profile?.full_name || undefined,
        metadata: {
          user_id: user.id,
        },
      });

      customerId = customer.id;

      // Update subscription with customer ID
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id);
    }

    // Create Stripe checkout session
    const stripe = createStripeClient();
    const origin = c.req.header("origin") || "http://localhost:3000";
    
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price: getStripePriceId(tier),
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${origin}/account?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/account`,
      metadata: {
        user_id: user.id,
        tier: tier,
      },
    });

    return c.json(createSuccessResponse({ checkout_url: session.url }));

  } catch (error) {
    logStep("api", "Error in create-checkout-session", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
}; 