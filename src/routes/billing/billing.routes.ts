// Billing route definitions following the reference pattern
// This file defines the API endpoints structure for billing operations

export const getSubscription = {
  path: "/billing/subscription",
  method: "get" as const,
  tags: ["Billing"],
  description: "Get user subscription details and usage information",
  responses: {
    200: {
      description: "Subscription details retrieved successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              subscribed: { type: "boolean" },
              subscription_tier: { type: "string" },
              subscription_end: { type: "string", nullable: true },
              usage: {
                type: "object",
                properties: {
                  messages_used: { type: "number" },
                  messages_limit: { type: "number" },
                  payg_credits_remaining: { type: "number" }
                }
              }
            }
          }
        }
      }
    },
    401: {
      description: "Unauthorized - user not authenticated"
    },
    500: {
      description: "Internal server error"
    }
  }
};

export const createCustomerPortal = {
  path: "/billing/customer-portal",
  method: "post" as const,
  tags: ["Billing"],
  description: "Create Stripe customer portal session",
  responses: {
    200: {
      description: "Customer portal URL created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              url: { type: "string" }
            }
          }
        }
      }
    },
    400: {
      description: "No Stripe customer found"
    },
    401: {
      description: "Unauthorized - user not authenticated"
    },
    500: {
      description: "Internal server error"
    }
  }
};

export const purchaseCredits = {
  path: "/billing/purchase-credits",
  method: "post" as const,
  tags: ["Billing"],
  description: "Purchase pay-as-you-go credits",
  requestBody: {
    required: true,
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            package_type: {
              type: "string",
              enum: ["starter", "writers", "pro"],
              description: "Type of credit package to purchase"
            }
          },
          required: ["package_type"]
        }
      }
    }
  },
  responses: {
    200: {
      description: "Checkout session created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              checkout_url: { type: "string" }
            }
          }
        }
      }
    },
    400: {
      description: "Invalid package type"
    },
    401: {
      description: "Unauthorized - user not authenticated"
    },
    500: {
      description: "Internal server error"
    }
  }
};

export const createCheckoutSession = {
  path: "/billing/create-checkout-session",
  method: "post" as const,
  tags: ["Billing"],
  description: "Create Stripe checkout session for subscription",
  requestBody: {
    required: true,
    content: {
      "application/json": {
        schema: {
          type: "object",
          properties: {
            tier: {
              type: "string",
              enum: ["Screenwriter", "Director", "Producer"],
              description: "Subscription tier to purchase"
            }
          },
          required: ["tier"]
        }
      }
    }
  },
  responses: {
    200: {
      description: "Checkout session created successfully",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              checkout_url: { type: "string" }
            }
          }
        }
      }
    },
    400: {
      description: "Invalid subscription tier"
    },
    401: {
      description: "Unauthorized - user not authenticated"
    },
    500: {
      description: "Internal server error"
    }
  }
};

// Type exports for handlers
export type GetSubscriptionRoute = typeof getSubscription;
export type CreateCustomerPortalRoute = typeof createCustomerPortal;
export type PurchaseCreditsRoute = typeof purchaseCredits;
export type CreateCheckoutSessionRoute = typeof createCheckoutSession; 