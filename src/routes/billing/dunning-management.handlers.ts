// Dunning management handlers
// Provides failed payment recovery and subscription retention functionality

import type { AppRouteHandler } from "../../shared/types";
import { createSupabaseClientFromContext, getAuthenticatedUser } from "../../shared/supabase";
import { logStep } from "../../shared/logger";
import { ERROR_CODES, ERROR_MESSAGES, createErrorResponse, createSuccessResponse } from "../../shared/constants";
import { 
  getDunningStatus, 
  retryFailedPayment, 
  updatePaymentMethod,
  sendDunningNotification,
  calculateDunningMetrics
} from "../../shared/dunning";

// Get dunning status for user's subscription
export const getDunningStatusHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Getting dunning status", { userId: user.id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(`
        stripe_subscription_id,
        failed_payment_count,
        last_payment_attempt,
        grace_period_end,
        subscription_status
      `)
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createSuccessResponse({
        hasPaymentIssues: false,
        message: "Free tier subscription - no payment issues"
      }));
    }
    
    // Get detailed dunning status from Stripe
    const dunningStatus = await getDunningStatus(subscription.stripe_subscription_id);
    
    if (!dunningStatus) {
      return c.json(createSuccessResponse({
        hasPaymentIssues: false,
        failureCount: 0,
        message: "No payment issues detected"
      }));
    }
    
    // Get payment failures from database
    const { data: paymentFailures } = await supabaseClient
      .from("payment_failures")
      .select("*")
      .eq("user_id", user.id)
      .is("resolved_at", null)
      .order("created_at", { ascending: false });
    
    return c.json(createSuccessResponse({
      hasPaymentIssues: true,
      ...dunningStatus,
      recentFailures: paymentFailures || [],
      canRetry: !dunningStatus.shouldCancel && !dunningStatus.shouldSuspend,
      recommendedAction: dunningStatus.shouldCancel ? 'update_payment_method' : 
                        dunningStatus.shouldSuspend ? 'contact_support' : 'retry_payment'
    }));
    
  } catch (error) {
    logStep("api", "Error getting dunning status", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Retry failed payment
export const retryPaymentHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { invoice_id } = await c.req.json();
    
    logStep("api", "Retrying failed payment", { userId: user.id, invoice_id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, id")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription || !subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("No active subscription found"), 404);
    }
    
    // Verify the invoice belongs to this user's subscription
    const { createStripeClient } = await import("../../shared/stripe");
    const stripe = createStripeClient();
    const invoice = await stripe.invoices.retrieve(invoice_id);
    
    if (invoice.subscription !== subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Invoice does not belong to your subscription"), 403);
    }
    
    // Attempt to retry the payment
    const result = await retryFailedPayment(subscription.stripe_subscription_id, invoice_id);
    
    if (result.success) {
      // Update local database to reflect successful payment
      await supabaseClient
        .from("user_subscriptions")
        .update({
          failed_payment_count: 0,
          last_payment_attempt: new Date().toISOString(),
          grace_period_end: null,
          subscription_status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq("user_id", user.id);
      
      // Mark payment failures as resolved
      await supabaseClient
        .from("payment_failures")
        .update({
          resolved_at: new Date().toISOString(),
          resolution_type: 'payment_succeeded'
        })
        .eq("user_id", user.id)
        .is("resolved_at", null);
      
      return c.json(createSuccessResponse({
        message: "Payment retry successful",
        paymentStatus: result.paymentStatus
      }));
    } else {
      return c.json(createErrorResponse(`Payment retry failed: ${result.paymentStatus}`), 400);
    }
    
  } catch (error) {
    logStep("api", "Error retrying payment", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Update payment method
export const updatePaymentMethodHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { payment_method_id } = await c.req.json();
    
    logStep("api", "Updating payment method", { userId: user.id });
    
    if (!payment_method_id) {
      return c.json(createErrorResponse("Payment method ID is required"), 400);
    }
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, stripe_customer_id")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription || !subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("No active subscription found"), 404);
    }
    
    // Update payment method
    const result = await updatePaymentMethod(subscription.stripe_subscription_id, payment_method_id);
    
    if (result.success) {
      // Reset failure count and grace period
      await supabaseClient
        .from("user_subscriptions")
        .update({
          failed_payment_count: 0,
          grace_period_end: null,
          subscription_status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq("user_id", user.id);
      
      return c.json(createSuccessResponse({
        message: result.message
      }));
    } else {
      return c.json(createErrorResponse(result.message), 400);
    }
    
  } catch (error) {
    logStep("api", "Error updating payment method", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Get payment failure history
export const getPaymentFailureHistoryHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Getting payment failure history", { userId: user.id });
    
    // Get payment failure history
    const { data: failures, error: failuresError } = await supabaseClient
      .from("payment_failures")
      .select(`
        id,
        stripe_invoice_id,
        failure_code,
        failure_message,
        decline_code,
        attempt_number,
        next_retry_date,
        resolved_at,
        resolution_type,
        created_at
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(20);
    
    if (failuresError) {
      return c.json(createErrorResponse("Failed to fetch payment history"), 500);
    }
    
    return c.json(createSuccessResponse({
      failures: failures || [],
      totalFailures: failures?.length || 0,
      unresolvedFailures: failures?.filter(f => !f.resolved_at).length || 0
    }));
    
  } catch (error) {
    logStep("api", "Error getting payment failure history", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Send dunning notification (manual trigger)
export const sendDunningNotificationHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { notification_type = 'payment_failed' } = await c.req.json();
    
    logStep("api", "Sending dunning notification", { userId: user.id, notification_type });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(`
        stripe_customer_id,
        failed_payment_count,
        grace_period_end
      `)
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription || !subscription.stripe_customer_id) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    // Send notification
    const success = await sendDunningNotification(
      subscription.stripe_customer_id,
      notification_type,
      {
        failureCount: subscription.failed_payment_count || 0,
        gracePeriodEnd: subscription.grace_period_end ? new Date(subscription.grace_period_end) : undefined
      }
    );
    
    if (success) {
      return c.json(createSuccessResponse({
        message: "Dunning notification sent successfully"
      }));
    } else {
      return c.json(createErrorResponse("Failed to send notification"), 500);
    }
    
  } catch (error) {
    logStep("api", "Error sending dunning notification", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Get dunning metrics (admin only)
export const getDunningMetricsHandler: AppRouteHandler = async (c) => {
  try {
    // This would typically require admin authentication
    // For now, we'll implement basic metrics
    
    const { start_date, end_date } = c.req.query();
    
    if (!start_date || !end_date) {
      return c.json(createErrorResponse("Start date and end date are required"), 400);
    }
    
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    
    logStep("api", "Getting dunning metrics", { start_date, end_date });
    
    const metrics = await calculateDunningMetrics(startDate, endDate);
    
    return c.json(createSuccessResponse(metrics));
    
  } catch (error) {
    logStep("api", "Error getting dunning metrics", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};
