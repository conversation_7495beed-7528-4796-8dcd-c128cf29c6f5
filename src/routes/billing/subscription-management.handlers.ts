// Enhanced subscription management handlers
// Provides comprehensive subscription lifecycle management

import type { AppRouteHandler } from "../../shared/types";
import { createSupabaseClientFromContext, getAuthenticatedUser, ensureUserSubscription } from "../../shared/supabase";
import { logStep } from "../../shared/logger";
import { createStripeClient } from "../../shared/stripe";
import { ERROR_CODES, ERROR_MESSAGES, createErrorResponse, createSuccessResponse } from "../../shared/constants";

// Pause subscription handler
export const pauseSubscriptionHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { pause_behavior = 'void', reason } = await c.req.json();
    
    logStep("api", "Pausing subscription", { userId: user.id, pause_behavior });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, subscription_status")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No active subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Cannot pause free tier subscription"), 400);
    }
    
    if (subscription.subscription_status === 'paused') {
      return c.json(createErrorResponse("Subscription is already paused"), 400);
    }
    
    // Pause subscription in Stripe
    const stripe = createStripeClient();
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      pause_collection: {
        behavior: pause_behavior
      },
      metadata: {
        pause_reason: reason || 'User requested pause'
      }
    });
    
    // Update local database (webhook will handle the actual status update)
    await supabaseClient.rpc("pause_subscription", {
      p_user_id: user.id,
      p_pause_behavior: pause_behavior,
      p_reason: reason
    });
    
    return c.json(createSuccessResponse({ 
      message: "Subscription paused successfully",
      pause_behavior 
    }));
    
  } catch (error) {
    logStep("api", "Error pausing subscription", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Resume subscription handler
export const resumeSubscriptionHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { reason } = await c.req.json();
    
    logStep("api", "Resuming subscription", { userId: user.id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, subscription_status")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Cannot resume free tier subscription"), 400);
    }
    
    if (subscription.subscription_status !== 'paused') {
      return c.json(createErrorResponse("Subscription is not paused"), 400);
    }
    
    // Resume subscription in Stripe
    const stripe = createStripeClient();
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      pause_collection: null,
      metadata: {
        resume_reason: reason || 'User requested resume'
      }
    });
    
    // Update local database (webhook will handle the actual status update)
    await supabaseClient.rpc("resume_subscription", {
      p_user_id: user.id,
      p_reason: reason
    });
    
    return c.json(createSuccessResponse({ 
      message: "Subscription resumed successfully" 
    }));
    
  } catch (error) {
    logStep("api", "Error resuming subscription", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Cancel subscription at period end handler
export const cancelAtPeriodEndHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { reason, immediate = false } = await c.req.json();
    
    logStep("api", "Canceling subscription", { userId: user.id, immediate });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, subscription_status, cancel_at_period_end")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Cannot cancel free tier subscription"), 400);
    }
    
    if (subscription.cancel_at_period_end) {
      return c.json(createErrorResponse("Subscription is already scheduled for cancellation"), 400);
    }
    
    const stripe = createStripeClient();
    
    if (immediate) {
      // Cancel immediately
      await stripe.subscriptions.cancel(subscription.stripe_subscription_id, {
        cancellation_details: {
          comment: reason || 'User requested immediate cancellation'
        }
      });
    } else {
      // Cancel at period end
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: true,
        cancellation_details: {
          comment: reason || 'User requested cancellation at period end'
        }
      });
    }
    
    return c.json(createSuccessResponse({ 
      message: immediate ? "Subscription canceled immediately" : "Subscription will cancel at the end of the current period",
      immediate
    }));
    
  } catch (error) {
    logStep("api", "Error canceling subscription", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Reactivate canceled subscription handler
export const reactivateSubscriptionHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Reactivating subscription", { userId: user.id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, subscription_status, cancel_at_period_end")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Cannot reactivate free tier subscription"), 400);
    }
    
    if (!subscription.cancel_at_period_end) {
      return c.json(createErrorResponse("Subscription is not scheduled for cancellation"), 400);
    }
    
    // Reactivate subscription in Stripe
    const stripe = createStripeClient();
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      cancel_at_period_end: false
    });
    
    return c.json(createSuccessResponse({ 
      message: "Subscription reactivated successfully" 
    }));
    
  } catch (error) {
    logStep("api", "Error reactivating subscription", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Preview subscription change handler
export const previewSubscriptionChangeHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { target_tier, billing_cycle } = await c.req.json();
    
    logStep("api", "Previewing subscription change", { userId: user.id, target_tier, billing_cycle });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, billing_cycle")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("Cannot modify free tier subscription"), 400);
    }
    
    // Get price ID for target tier
    const { getStripePriceId } = await import("../../shared/pricing");
    const targetPriceId = getStripePriceId(target_tier, billing_cycle);
    
    if (!targetPriceId) {
      return c.json(createErrorResponse("Invalid tier or billing cycle"), 400);
    }
    
    // Preview the change in Stripe
    const stripe = createStripeClient();
    const preview = await stripe.invoices.retrieveUpcoming({
      customer: subscription.stripe_customer_id,
      subscription: subscription.stripe_subscription_id,
      subscription_items: [{
        id: subscription.stripe_subscription_item_id,
        price: targetPriceId
      }],
      subscription_proration_behavior: 'create_prorations'
    });
    
    return c.json(createSuccessResponse({
      current_tier: subscription.subscription_tier,
      target_tier,
      current_billing_cycle: subscription.billing_cycle,
      target_billing_cycle: billing_cycle,
      proration_amount: preview.amount_due,
      next_invoice_date: preview.period_end,
      line_items: preview.lines.data.map(item => ({
        description: item.description,
        amount: item.amount,
        proration: item.proration
      }))
    }));
    
  } catch (error) {
    logStep("api", "Error previewing subscription change", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Get subscription history handler
export const getSubscriptionHistoryHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);

    logStep("api", "Getting subscription history", { userId: user.id });

    // Get subscription change history
    const { data: changes, error: historyError } = await supabaseClient
      .from("subscription_change_history")
      .select(`
        id,
        change_type,
        from_tier,
        to_tier,
        from_billing_cycle,
        to_billing_cycle,
        proration_amount_cents,
        effective_date,
        change_reason,
        created_at
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (historyError) {
      logStep("api", "Error fetching subscription history", { error: historyError.message });
      return c.json(createErrorResponse("Failed to fetch subscription history"), 500);
    }

    return c.json(createSuccessResponse({
      changes: changes || []
    }));

  } catch (error) {
    logStep("api", "Error getting subscription history", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};
