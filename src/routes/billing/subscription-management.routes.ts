// Enhanced subscription management route definitions
// Provides type-safe route definitions for subscription lifecycle management

import { createRoute, z } from "@hono/zod-openapi";

// Pause subscription route
export const pauseSubscriptionRoute = createRoute({
  method: "post",
  path: "/billing/subscription/pause",
  summary: "Pause active subscription",
  description: "Pause the user's active subscription with specified behavior",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            pause_behavior: z.enum(["keep_as_draft", "mark_uncollectible", "void"]).default("void"),
            reason: z.string().optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              message: z.string(),
              pause_behavior: z.string()
            })
          })
        }
      },
      description: "Subscription paused successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "Bad request - invalid subscription state"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No active subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Resume subscription route
export const resumeSubscriptionRoute = createRoute({
  method: "post",
  path: "/billing/subscription/resume",
  summary: "Resume paused subscription",
  description: "Resume a previously paused subscription",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            reason: z.string().optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              message: z.string()
            })
          })
        }
      },
      description: "Subscription resumed successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "Bad request - subscription not paused"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Cancel subscription route
export const cancelSubscriptionRoute = createRoute({
  method: "post",
  path: "/billing/subscription/cancel",
  summary: "Cancel subscription",
  description: "Cancel subscription immediately or at the end of the current period",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            immediate: z.boolean().default(false),
            reason: z.string().optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              message: z.string(),
              immediate: z.boolean()
            })
          })
        }
      },
      description: "Subscription canceled successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "Bad request - invalid subscription state"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Reactivate subscription route
export const reactivateSubscriptionRoute = createRoute({
  method: "post",
  path: "/billing/subscription/reactivate",
  summary: "Reactivate canceled subscription",
  description: "Reactivate a subscription that was scheduled for cancellation",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              message: z.string()
            })
          })
        }
      },
      description: "Subscription reactivated successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "Bad request - subscription not scheduled for cancellation"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Preview subscription change route
export const previewSubscriptionChangeRoute = createRoute({
  method: "post",
  path: "/billing/subscription/preview-change",
  summary: "Preview subscription change",
  description: "Preview the proration and billing impact of changing subscription tier or billing cycle",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            target_tier: z.enum(["Reserved Seat", "VIP Table"]),
            billing_cycle: z.enum(["monthly", "annual"]).default("monthly")
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              current_tier: z.string(),
              target_tier: z.string(),
              current_billing_cycle: z.string(),
              target_billing_cycle: z.string(),
              proration_amount: z.number(),
              next_invoice_date: z.number(),
              line_items: z.array(z.object({
                description: z.string(),
                amount: z.number(),
                proration: z.boolean()
              }))
            })
          })
        }
      },
      description: "Subscription change preview generated successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "Bad request - invalid tier or billing cycle"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Get subscription history route
export const getSubscriptionHistoryRoute = createRoute({
  method: "get",
  path: "/billing/subscription/history",
  summary: "Get subscription change history",
  description: "Retrieve the complete history of subscription changes for the authenticated user",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              changes: z.array(z.object({
                id: z.string(),
                change_type: z.string(),
                from_tier: z.string().nullable(),
                to_tier: z.string().nullable(),
                from_billing_cycle: z.string().nullable(),
                to_billing_cycle: z.string().nullable(),
                proration_amount_cents: z.number(),
                effective_date: z.string(),
                change_reason: z.string().nullable(),
                created_at: z.string()
              }))
            })
          })
        }
      },
      description: "Subscription history retrieved successfully"
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            error: z.string()
          })
        }
      },
      description: "No subscription found"
    }
  },
  tags: ["Subscription Management"]
});

// Type exports for handlers
export type PauseSubscriptionRoute = typeof pauseSubscriptionRoute;
export type ResumeSubscriptionRoute = typeof resumeSubscriptionRoute;
export type CancelSubscriptionRoute = typeof cancelSubscriptionRoute;
export type ReactivateSubscriptionRoute = typeof reactivateSubscriptionRoute;
export type PreviewSubscriptionChangeRoute = typeof previewSubscriptionChangeRoute;
export type GetSubscriptionHistoryRoute = typeof getSubscriptionHistoryRoute;
