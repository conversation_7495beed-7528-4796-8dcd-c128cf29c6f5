// Trial management handlers
// Provides trial period management functionality

import type { AppRouteHand<PERSON> } from "../../shared/types";
import { createSupabaseClientFromContext, getAuthenticatedUser } from "../../shared/supabase";
import { logStep } from "../../shared/logger";
import { ERROR_CODES, ERROR_MESSAGES, createErrorResponse, createSuccessResponse } from "../../shared/constants";
import { getTrialInfo, extendTrial, createSubscriptionWithTrial } from "../../shared/proration";

// Get trial information handler
export const getTrialInfoHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    logStep("api", "Getting trial information", { userId: user.id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, trial_start_date, trial_end_date, subscription_tier")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    if (!subscription.stripe_subscription_id) {
      // Free tier user - check if eligible for trial
      return c.json(createSuccessResponse({
        isInTrial: false,
        trialEligible: subscription.subscription_tier === 'Sample the Table',
        canStartTrial: subscription.subscription_tier === 'Sample the Table',
        trialDaysAvailable: 7
      }));
    }
    
    // Get trial info from Stripe
    const trialInfo = await getTrialInfo(subscription.stripe_subscription_id);
    
    return c.json(createSuccessResponse({
      ...trialInfo,
      trialEligible: false,
      canStartTrial: false,
      trialDaysAvailable: 0
    }));
    
  } catch (error) {
    logStep("api", "Error getting trial information", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Extend trial handler
export const extendTrialHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { additional_days = 3, reason } = await c.req.json();
    
    logStep("api", "Extending trial", { userId: user.id, additional_days });
    
    // Validate additional days (max 7 days extension)
    if (additional_days < 1 || additional_days > 7) {
      return c.json(createErrorResponse("Additional days must be between 1 and 7"), 400);
    }
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, id")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription || !subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("No active subscription found"), 404);
    }
    
    // Extend trial in Stripe
    const result = await extendTrial(subscription.stripe_subscription_id, additional_days);
    
    // Update local database
    await supabaseClient
      .from("user_subscriptions")
      .update({
        trial_end_date: result.newTrialEnd.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq("user_id", user.id);
    
    // Log the extension
    await supabaseClient.rpc("log_subscription_change", {
      p_user_id: user.id,
      p_subscription_id: subscription.id,
      p_change_type: 'trial_extension',
      p_stripe_subscription_id: subscription.stripe_subscription_id,
      p_change_reason: reason || `Trial extended by ${additional_days} days`,
      p_change_metadata: JSON.stringify({ additional_days })
    });
    
    return c.json(createSuccessResponse({
      message: `Trial extended by ${additional_days} days`,
      newTrialEnd: result.newTrialEnd.toISOString(),
      additionalDays: additional_days
    }));
    
  } catch (error) {
    logStep("api", "Error extending trial", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Start trial for free tier users
export const startTrialHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { target_tier, trial_days = 7 } = await c.req.json();
    
    logStep("api", "Starting trial", { userId: user.id, target_tier, trial_days });
    
    // Validate trial days (max 14 days)
    if (trial_days < 1 || trial_days > 14) {
      return c.json(createErrorResponse("Trial days must be between 1 and 14"), 400);
    }
    
    // Validate target tier
    if (!['Reserved Seat', 'VIP Table'].includes(target_tier)) {
      return c.json(createErrorResponse("Invalid target tier for trial"), 400);
    }
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, stripe_customer_id, id")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription) {
      return c.json(createErrorResponse("No subscription found"), 404);
    }
    
    // Check if user is eligible for trial
    if (subscription.subscription_tier !== 'Sample the Table') {
      return c.json(createErrorResponse("Only free tier users can start trials"), 400);
    }
    
    if (subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("User already has an active subscription"), 400);
    }
    
    // Get or create Stripe customer
    let customerId = subscription.stripe_customer_id;
    
    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single();
      
      const { createStripeClient } = await import("../../shared/stripe");
      const stripe = createStripeClient();
      const customer = await stripe.customers.create({
        email: user.email,
        name: profile?.full_name || undefined,
        metadata: {
          user_id: user.id,
        },
      });
      
      customerId = customer.id;
      
      // Update subscription with customer ID
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id);
    }
    
    // Get price ID for target tier
    const { getStripePriceId } = await import("../../shared/pricing");
    const priceId = getStripePriceId(target_tier, 'monthly');
    
    if (!priceId) {
      return c.json(createErrorResponse("Invalid pricing configuration"), 500);
    }
    
    // Create subscription with trial
    const subscriptionId = await createSubscriptionWithTrial(
      customerId,
      priceId,
      trial_days,
      {
        user_id: user.id,
        subscription_tier: target_tier,
        billing_cycle: 'monthly',
        trial_type: 'initial'
      }
    );
    
    // Update tier limits for trial
    await supabaseClient.rpc("update_subscription_tier_limits", {
      user_uuid: user.id,
      new_tier: target_tier
    });
    
    // Update subscription record
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + trial_days);
    
    await supabaseClient
      .from("user_subscriptions")
      .update({
        stripe_subscription_id: subscriptionId,
        subscription_tier: target_tier,
        subscription_status: 'trialing',
        trial_start_date: new Date().toISOString(),
        trial_end_date: trialEnd.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq("user_id", user.id);
    
    // Log trial start
    await supabaseClient.rpc("log_subscription_change", {
      p_user_id: user.id,
      p_subscription_id: subscription.id,
      p_change_type: 'trial_start',
      p_from_tier: 'Sample the Table',
      p_to_tier: target_tier,
      p_stripe_subscription_id: subscriptionId,
      p_change_reason: `Started ${trial_days}-day trial`,
      p_change_metadata: JSON.stringify({ trial_days, trial_type: 'initial' })
    });
    
    return c.json(createSuccessResponse({
      message: `${trial_days}-day trial started successfully`,
      subscriptionId,
      targetTier: target_tier,
      trialDays: trial_days,
      trialEnd: trialEnd.toISOString()
    }));
    
  } catch (error) {
    logStep("api", "Error starting trial", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};

// Cancel trial handler
export const cancelTrialHandler: AppRouteHandler = async (c) => {
  try {
    const user = await getAuthenticatedUser(c);
    const supabaseClient = createSupabaseClientFromContext(c);
    
    const { reason } = await c.req.json();
    
    logStep("api", "Canceling trial", { userId: user.id });
    
    // Get current subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("stripe_subscription_id, subscription_tier, subscription_status, id")
      .eq("user_id", user.id)
      .single();
    
    if (subError || !subscription || !subscription.stripe_subscription_id) {
      return c.json(createErrorResponse("No active subscription found"), 404);
    }
    
    if (subscription.subscription_status !== 'trialing') {
      return c.json(createErrorResponse("Subscription is not in trial"), 400);
    }
    
    // Cancel subscription in Stripe
    const { createStripeClient } = await import("../../shared/stripe");
    const stripe = createStripeClient();
    await stripe.subscriptions.cancel(subscription.stripe_subscription_id);
    
    // Downgrade to free tier
    await supabaseClient.rpc("update_subscription_tier_limits", {
      user_uuid: user.id,
      new_tier: "Sample the Table"
    });
    
    // Update subscription record
    await supabaseClient
      .from("user_subscriptions")
      .update({
        stripe_subscription_id: null,
        subscription_tier: 'Sample the Table',
        subscription_status: 'active',
        trial_start_date: null,
        trial_end_date: null,
        canceled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq("user_id", user.id);
    
    // Log trial cancellation
    await supabaseClient.rpc("log_subscription_change", {
      p_user_id: user.id,
      p_subscription_id: subscription.id,
      p_change_type: 'trial_cancel',
      p_from_tier: subscription.subscription_tier,
      p_to_tier: 'Sample the Table',
      p_change_reason: reason || 'Trial canceled by user'
    });
    
    return c.json(createSuccessResponse({
      message: "Trial canceled successfully",
      newTier: 'Sample the Table'
    }));
    
  } catch (error) {
    logStep("api", "Error canceling trial", { error: (error as Error).message });
    return c.json(createErrorResponse((error as Error).message), 500);
  }
};
