// Error codes and messages
export const ERROR_CODES = {
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN", 
  NOT_FOUND: "NOT_FOUND",
  VALIDATION_ERROR: "VAL<PERSON>ATION_ERROR",
  INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR",
  USAGE_LIMIT_EXCEEDED: "USAGE_LIMIT_EXCEEDED"
} as const;

export const ERROR_MESSAGES = {
  UNAUTHORIZED: "User not authenticated",
  FORBIDDEN: "Access denied",
  NOT_FOUND: "Resource not found",
  VALIDATION_ERROR: "Invalid input data",
  INTERNAL_SERVER_ERROR: "Internal server error",
  USAGE_LIMIT_EXCEEDED: "Usage limit exceeded"
} as const;

// Common response schemas
export const createErrorResponse = (message: string, code?: string) => ({
  error: message,
  code: code || ERROR_CODES.INTERNAL_SERVER_ERROR
});

export const createSuccessResponse = <T>(data: T, message?: string) => ({
  data,
  message: message || "Success"
}); 