import type { SupabaseClient } from '@supabase/supabase-js';
import type { Context } from 'hono';
import { logStep } from './logger';
import { getEnvironmentContext, type EnvironmentContext } from '../middleware/environment';

/**
 * Interface for table schema information
 */
interface TableSchema {
  tableName: string;
  columns: ColumnInfo[];
  indexes: IndexInfo[];
  constraints: ConstraintInfo[];
}

interface ColumnInfo {
  columnName: string;
  dataType: string;
  isNullable: boolean;
  defaultValue?: string;
  isIdentity?: boolean;
}

interface IndexInfo {
  indexName: string;
  columns: string[];
  isUnique: boolean;
}

interface ConstraintInfo {
  constraintName: string;
  constraintType: string;
  definition: string;
}

/**
 * Get the appropriate table name based on environment context
 * 
 * @param baseTableName - The base table name (e.g., 'users', 'messages')
 * @param context - Environment context
 * @returns The environment-appropriate table name
 */
export const getTableName = (baseTableName: string, context: EnvironmentContext): string => {
  return context.isTestEnvironment ? `${context.tablePrefix}${baseTableName}` : baseTableName;
};

/**
 * Get table name from Hono context
 * 
 * @param baseTableName - The base table name
 * @param c - Hono context
 * @returns The environment-appropriate table name
 */
export const getTableNameFromContext = (baseTableName: string, c: Context): string => {
  const context = getEnvironmentContext(c);
  return getTableName(baseTableName, context);
};

/**
 * Check if a table exists in the database
 * 
 * @param tableName - Name of the table to check
 * @param supabaseClient - Supabase client instance
 * @returns Promise<boolean> - true if table exists, false otherwise
 */
export const tableExists = async (tableName: string, supabaseClient: SupabaseClient): Promise<boolean> => {
  try {
    const { data, error } = await supabaseClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .single();

    if (error && error.code !== 'PGRST116') {
      logStep('database', 'Error checking table existence', { tableName, error: error.message });
      return false;
    }

    return !!data;
  } catch (error) {
    logStep('database', 'Exception checking table existence', { tableName, error: (error as Error).message });
    return false;
  }
};

/**
 * Get table schema information
 * 
 * @param tableName - Name of the table
 * @param supabaseClient - Supabase client instance
 * @returns Promise<TableSchema | null> - Table schema or null if not found
 */
export const getTableSchema = async (tableName: string, supabaseClient: SupabaseClient): Promise<TableSchema | null> => {
  try {
    // Get column information
    const { data: columns, error: columnsError } = await supabaseClient
      .from('information_schema.columns')
      .select(`
        column_name,
        data_type,
        is_nullable,
        column_default,
        is_identity
      `)
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');

    if (columnsError) {
      logStep('database', 'Error fetching table columns', { tableName, error: columnsError.message });
      return null;
    }

    if (!columns || columns.length === 0) {
      logStep('database', 'Table not found or has no columns', { tableName });
      return null;
    }

    // Transform column data
    const columnInfo: ColumnInfo[] = columns.map(col => ({
      columnName: col.column_name,
      dataType: col.data_type,
      isNullable: col.is_nullable === 'YES',
      defaultValue: col.column_default,
      isIdentity: col.is_identity === 'YES'
    }));

    // For now, return basic schema (indexes and constraints can be added later)
    return {
      tableName,
      columns: columnInfo,
      indexes: [], // TODO: Implement index fetching
      constraints: [] // TODO: Implement constraint fetching
    };

  } catch (error) {
    logStep('database', 'Exception getting table schema', { tableName, error: (error as Error).message });
    return null;
  }
};

/**
 * Create a test table based on the production table schema
 * 
 * @param baseTableName - The base table name (without _test_ prefix)
 * @param supabaseClient - Supabase client instance
 * @returns Promise<boolean> - true if successful, false otherwise
 */
export const createTestTable = async (baseTableName: string, supabaseClient: SupabaseClient): Promise<boolean> => {
  const testTableName = `_test_${baseTableName}`;
  
  try {
    logStep('database', 'Creating test table', { baseTableName, testTableName });

    // Check if test table already exists
    const testTableExists = await tableExists(testTableName, supabaseClient);
    if (testTableExists) {
      logStep('database', 'Test table already exists', { testTableName });
      return true;
    }

    // Get production table schema
    const schema = await getTableSchema(baseTableName, supabaseClient);
    if (!schema) {
      logStep('database', 'Cannot create test table - base table schema not found', { baseTableName });
      return false;
    }

    // Create test table using SQL
    const createTableSQL = generateCreateTableSQL(testTableName, schema);
    
    const { error } = await supabaseClient.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      logStep('database', 'Error creating test table', { testTableName, error: error.message });
      return false;
    }

    logStep('database', 'Test table created successfully', { testTableName });
    return true;

  } catch (error) {
    logStep('database', 'Exception creating test table', { 
      baseTableName, 
      testTableName, 
      error: (error as Error).message 
    });
    return false;
  }
};

/**
 * Generate CREATE TABLE SQL from schema
 * 
 * @param tableName - Name of the table to create
 * @param schema - Table schema information
 * @returns SQL string for creating the table
 */
const generateCreateTableSQL = (tableName: string, schema: TableSchema): string => {
  const columnDefinitions = schema.columns.map(col => {
    let definition = `"${col.columnName}" ${col.dataType}`;
    
    if (!col.isNullable) {
      definition += ' NOT NULL';
    }
    
    if (col.defaultValue && !col.isIdentity) {
      definition += ` DEFAULT ${col.defaultValue}`;
    }
    
    if (col.isIdentity) {
      definition += ' GENERATED BY DEFAULT AS IDENTITY';
    }
    
    return definition;
  }).join(',\n  ');

  return `CREATE TABLE IF NOT EXISTS "${tableName}" (\n  ${columnDefinitions}\n);`;
};

/**
 * Ensure test table exists for a given base table
 * Creates the test table if it doesn't exist
 * 
 * @param baseTableName - The base table name
 * @param supabaseClient - Supabase client instance
 * @returns Promise<boolean> - true if test table exists or was created successfully
 */
export const ensureTestTable = async (baseTableName: string, supabaseClient: SupabaseClient): Promise<boolean> => {
  const testTableName = `_test_${baseTableName}`;
  
  // Check if test table exists
  const exists = await tableExists(testTableName, supabaseClient);
  if (exists) {
    return true;
  }
  
  // Create test table if it doesn't exist
  return await createTestTable(baseTableName, supabaseClient);
};

/**
 * Sync schema changes from production table to test table
 * 
 * @param baseTableName - The base table name
 * @param supabaseClient - Supabase client instance
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export const syncTestTableSchema = async (baseTableName: string, supabaseClient: SupabaseClient): Promise<boolean> => {
  const testTableName = `_test_${baseTableName}`;
  
  try {
    logStep('database', 'Syncing test table schema', { baseTableName, testTableName });

    // Get schemas for both tables
    const [baseSchema, testSchema] = await Promise.all([
      getTableSchema(baseTableName, supabaseClient),
      getTableSchema(testTableName, supabaseClient)
    ]);

    if (!baseSchema) {
      logStep('database', 'Base table schema not found for sync', { baseTableName });
      return false;
    }

    if (!testSchema) {
      // Test table doesn't exist, create it
      return await createTestTable(baseTableName, supabaseClient);
    }

    // Compare schemas and apply differences
    // For now, this is a placeholder - full implementation would compare columns,
    // indexes, and constraints and generate appropriate ALTER TABLE statements
    
    logStep('database', 'Schema sync completed', { baseTableName, testTableName });
    return true;

  } catch (error) {
    logStep('database', 'Exception syncing test table schema', { 
      baseTableName, 
      testTableName, 
      error: (error as Error).message 
    });
    return false;
  }
};

/**
 * List of all known tables in the system
 * This should be updated when new tables are added
 */
export const KNOWN_TABLES = [
  'user_subscriptions',
  'characters',
  'conversations',
  'messages',
  'waitlist',
  'invite_codes',
  'subscription_metrics',
  'subscription_change_history'
] as const;

/**
 * Create test tables for all known tables
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<{ success: string[], failed: string[] }> - Results of table creation
 */
export const createAllTestTables = async (supabaseClient: SupabaseClient): Promise<{ success: string[], failed: string[] }> => {
  const results = { success: [] as string[], failed: [] as string[] };
  
  logStep('database', 'Creating all test tables', { tableCount: KNOWN_TABLES.length });
  
  for (const tableName of KNOWN_TABLES) {
    const success = await createTestTable(tableName, supabaseClient);
    if (success) {
      results.success.push(tableName);
    } else {
      results.failed.push(tableName);
    }
  }
  
  logStep('database', 'Test table creation completed', results);
  return results;
};

/**
 * Clean up test data from all test tables
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<boolean> - true if cleanup successful
 */
export const cleanupTestData = async (supabaseClient: SupabaseClient): Promise<boolean> => {
  try {
    logStep('database', 'Starting test data cleanup', {});
    
    for (const tableName of KNOWN_TABLES) {
      const testTableName = `_test_${tableName}`;
      const exists = await tableExists(testTableName, supabaseClient);
      
      if (exists) {
        const { error } = await supabaseClient
          .from(testTableName)
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
        
        if (error) {
          logStep('database', 'Error cleaning test table', { testTableName, error: error.message });
        } else {
          logStep('database', 'Test table cleaned', { testTableName });
        }
      }
    }
    
    logStep('database', 'Test data cleanup completed', {});
    return true;
    
  } catch (error) {
    logStep('database', 'Exception during test data cleanup', { error: (error as Error).message });
    return false;
  }
};
