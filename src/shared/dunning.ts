// Dunning management utilities
// Handles failed payment recovery and subscription retention

import { createStripeClient } from "./stripe";
import { logStep } from "./logger";

export interface DunningSettings {
  maxRetries: number;
  retryIntervalDays: number[];
  gracePeriodDays: number;
  suspensionThresholdDays: number;
  cancellationThresholdDays: number;
}

export interface PaymentFailureInfo {
  failureCount: number;
  lastFailureDate: Date;
  nextRetryDate?: Date;
  gracePeriodEnd?: Date;
  isInGracePeriod: boolean;
  shouldSuspend: boolean;
  shouldCancel: boolean;
  failureReason?: string;
  declineCode?: string;
}

// Default dunning settings
export const DEFAULT_DUNNING_SETTINGS: DunningSettings = {
  maxRetries: 4,
  retryIntervalDays: [3, 5, 7, 14], // Retry after 3, 5, 7, and 14 days
  gracePeriodDays: 7,
  suspensionThresholdDays: 14,
  cancellationThresholdDays: 30
};

// Analyze payment failure and determine next actions
export const analyzePaymentFailure = (
  failureCount: number,
  lastFailureDate: Date,
  settings: DunningSettings = DEFAULT_DUNNING_SETTINGS
): PaymentFailureInfo => {
  const now = new Date();
  const daysSinceFailure = Math.floor((now.getTime() - lastFailureDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Calculate grace period end
  const gracePeriodEnd = new Date(lastFailureDate);
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + settings.gracePeriodDays);
  
  // Calculate next retry date
  let nextRetryDate: Date | undefined;
  if (failureCount <= settings.maxRetries && failureCount <= settings.retryIntervalDays.length) {
    nextRetryDate = new Date(lastFailureDate);
    nextRetryDate.setDate(nextRetryDate.getDate() + settings.retryIntervalDays[failureCount - 1]);
  }
  
  return {
    failureCount,
    lastFailureDate,
    nextRetryDate,
    gracePeriodEnd,
    isInGracePeriod: now <= gracePeriodEnd,
    shouldSuspend: daysSinceFailure >= settings.suspensionThresholdDays,
    shouldCancel: daysSinceFailure >= settings.cancellationThresholdDays,
  };
};

// Process payment failure and update subscription
export const processPaymentFailure = async (
  subscriptionId: string,
  invoiceId: string,
  failureCode?: string,
  failureMessage?: string,
  declineCode?: string
): Promise<{ success: boolean; nextAction: string; retryDate?: Date }> => {
  try {
    const stripe = createStripeClient();
    
    // Get subscription details
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const invoice = await stripe.invoices.retrieve(invoiceId);
    
    // Get current failure count from subscription metadata
    const currentFailureCount = parseInt(subscription.metadata?.failure_count || '0');
    const newFailureCount = currentFailureCount + 1;
    
    // Analyze the failure
    const failureInfo = analyzePaymentFailure(newFailureCount, new Date());
    
    let nextAction = 'retry';
    let updateData: any = {
      metadata: {
        ...subscription.metadata,
        failure_count: newFailureCount.toString(),
        last_failure_date: new Date().toISOString(),
        failure_code: failureCode || '',
        decline_code: declineCode || ''
      }
    };
    
    // Determine next action based on failure analysis
    if (failureInfo.shouldCancel) {
      // Cancel subscription after too many failures
      await stripe.subscriptions.cancel(subscriptionId, {
        cancellation_details: {
          comment: `Canceled due to repeated payment failures (${newFailureCount} failures)`
        }
      });
      nextAction = 'canceled';
    } else if (failureInfo.shouldSuspend) {
      // Suspend subscription but don't cancel yet
      updateData.pause_collection = { behavior: 'mark_uncollectible' };
      nextAction = 'suspended';
    } else if (failureInfo.nextRetryDate) {
      // Schedule next retry
      updateData.metadata.next_retry_date = failureInfo.nextRetryDate.toISOString();
      nextAction = 'retry_scheduled';
    }
    
    // Update subscription with failure information
    if (nextAction !== 'canceled') {
      await stripe.subscriptions.update(subscriptionId, updateData);
    }
    
    logStep("dunning", "Payment failure processed", {
      subscriptionId,
      failureCount: newFailureCount,
      nextAction,
      retryDate: failureInfo.nextRetryDate?.toISOString()
    });
    
    return {
      success: true,
      nextAction,
      retryDate: failureInfo.nextRetryDate
    };
    
  } catch (error) {
    logStep("dunning", "Error processing payment failure", { error: (error as Error).message });
    throw new Error(`Failed to process payment failure: ${(error as Error).message}`);
  }
};

// Retry failed payment
export const retryFailedPayment = async (
  subscriptionId: string,
  invoiceId: string
): Promise<{ success: boolean; paymentStatus: string }> => {
  try {
    const stripe = createStripeClient();
    
    // Get the failed invoice
    const invoice = await stripe.invoices.retrieve(invoiceId);
    
    if (invoice.status !== 'open') {
      throw new Error(`Invoice is not in open status: ${invoice.status}`);
    }
    
    // Attempt to pay the invoice
    const paidInvoice = await stripe.invoices.pay(invoiceId, {
      forgive: false, // Don't forgive the amount
      paid_out_of_band: false
    });
    
    if (paidInvoice.status === 'paid') {
      // Payment succeeded - reset failure count
      await stripe.subscriptions.update(subscriptionId, {
        metadata: {
          failure_count: '0',
          last_failure_date: '',
          next_retry_date: ''
        },
        pause_collection: null // Resume collection if paused
      });
      
      logStep("dunning", "Payment retry successful", {
        subscriptionId,
        invoiceId
      });
      
      return { success: true, paymentStatus: 'paid' };
    } else {
      logStep("dunning", "Payment retry failed", {
        subscriptionId,
        invoiceId,
        status: paidInvoice.status
      });
      
      return { success: false, paymentStatus: paidInvoice.status };
    }
    
  } catch (error) {
    logStep("dunning", "Error retrying payment", { error: (error as Error).message });
    return { success: false, paymentStatus: 'failed' };
  }
};

// Get dunning status for a subscription
export const getDunningStatus = async (subscriptionId: string): Promise<PaymentFailureInfo | null> => {
  try {
    const stripe = createStripeClient();
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    const failureCount = parseInt(subscription.metadata?.failure_count || '0');
    const lastFailureDateStr = subscription.metadata?.last_failure_date;
    
    if (failureCount === 0 || !lastFailureDateStr) {
      return null; // No payment failures
    }
    
    const lastFailureDate = new Date(lastFailureDateStr);
    const failureInfo = analyzePaymentFailure(failureCount, lastFailureDate);
    
    return {
      ...failureInfo,
      failureReason: subscription.metadata?.failure_code,
      declineCode: subscription.metadata?.decline_code
    };
    
  } catch (error) {
    logStep("dunning", "Error getting dunning status", { error: (error as Error).message });
    return null;
  }
};

// Send dunning notification (placeholder for email integration)
export const sendDunningNotification = async (
  customerId: string,
  notificationType: 'payment_failed' | 'retry_reminder' | 'final_notice' | 'suspension_notice',
  context: {
    failureCount: number;
    nextRetryDate?: Date;
    gracePeriodEnd?: Date;
    amount?: number;
  }
): Promise<boolean> => {
  try {
    const stripe = createStripeClient();
    const customer = await stripe.customers.retrieve(customerId);
    
    if (!customer || customer.deleted) {
      throw new Error('Customer not found');
    }
    
    // Log notification (in a real implementation, this would send an email)
    logStep("dunning", "Dunning notification sent", {
      customerId,
      email: (customer as any).email,
      notificationType,
      context
    });
    
    // TODO: Integrate with email service (SendGrid, AWS SES, etc.)
    // This is where you would send the actual notification email
    
    return true;
    
  } catch (error) {
    logStep("dunning", "Error sending dunning notification", { error: (error as Error).message });
    return false;
  }
};

// Update payment method for failed subscription
export const updatePaymentMethod = async (
  subscriptionId: string,
  paymentMethodId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const stripe = createStripeClient();
    
    // Get subscription
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    // Update default payment method for customer
    await stripe.customers.update(subscription.customer as string, {
      invoice_settings: {
        default_payment_method: paymentMethodId
      }
    });
    
    // Update subscription payment method
    await stripe.subscriptions.update(subscriptionId, {
      default_payment_method: paymentMethodId,
      pause_collection: null, // Resume collection if paused
      metadata: {
        ...subscription.metadata,
        payment_method_updated: new Date().toISOString()
      }
    });
    
    logStep("dunning", "Payment method updated", {
      subscriptionId,
      paymentMethodId
    });
    
    return {
      success: true,
      message: "Payment method updated successfully"
    };
    
  } catch (error) {
    logStep("dunning", "Error updating payment method", { error: (error as Error).message });
    return {
      success: false,
      message: (error as Error).message
    };
  }
};

// Calculate dunning metrics for analytics
export const calculateDunningMetrics = async (
  startDate: Date,
  endDate: Date
): Promise<{
  totalFailures: number;
  recoveredPayments: number;
  suspendedSubscriptions: number;
  canceledSubscriptions: number;
  recoveryRate: number;
}> => {
  try {
    const stripe = createStripeClient();
    
    // Get failed invoices in date range
    const failedInvoices = await stripe.invoices.list({
      status: 'open',
      created: {
        gte: Math.floor(startDate.getTime() / 1000),
        lte: Math.floor(endDate.getTime() / 1000)
      },
      limit: 100
    });
    
    // Get paid invoices that were previously failed
    const paidInvoices = await stripe.invoices.list({
      status: 'paid',
      created: {
        gte: Math.floor(startDate.getTime() / 1000),
        lte: Math.floor(endDate.getTime() / 1000)
      },
      limit: 100
    });
    
    const totalFailures = failedInvoices.data.length;
    const recoveredPayments = paidInvoices.data.filter(invoice => 
      invoice.metadata?.was_failed === 'true'
    ).length;
    
    const recoveryRate = totalFailures > 0 ? (recoveredPayments / totalFailures) * 100 : 0;
    
    return {
      totalFailures,
      recoveredPayments,
      suspendedSubscriptions: 0, // Would need to query subscriptions with pause_collection
      canceledSubscriptions: 0, // Would need to query canceled subscriptions
      recoveryRate
    };
    
  } catch (error) {
    logStep("dunning", "Error calculating dunning metrics", { error: (error as Error).message });
    return {
      totalFailures: 0,
      recoveredPayments: 0,
      suspendedSubscriptions: 0,
      canceledSubscriptions: 0,
      recoveryRate: 0
    };
  }
};
