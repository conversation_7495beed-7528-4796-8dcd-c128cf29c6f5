import type { SupabaseClient } from '@supabase/supabase-js';
import { logStep } from './logger';
import { 
  KNOWN_TABLES, 
  createAllTestTables, 
  cleanupTestData, 
  tableExists, 
  getTableSchema,
  ensureTestTable,
  syncTestTableSchema 
} from './database-utils';

/**
 * Migration result interface
 */
interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Validation result interface
 */
interface ValidationResult {
  isValid: boolean;
  issues: string[];
  warnings: string[];
}

/**
 * Initialize test environment by creating all necessary test tables
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<MigrationResult> - Result of initialization
 */
export const initializeTestEnvironment = async (supabaseClient: SupabaseClient): Promise<MigrationResult> => {
  try {
    logStep('migration', 'Initializing test environment', { tableCount: KNOWN_TABLES.length });

    // Create all test tables
    const results = await createAllTestTables(supabaseClient);
    
    if (results.failed.length > 0) {
      return {
        success: false,
        message: `Failed to create ${results.failed.length} test tables`,
        details: {
          successful: results.success,
          failed: results.failed
        }
      };
    }

    // Validate test environment
    const validation = await validateTestEnvironment(supabaseClient);
    
    if (!validation.isValid) {
      return {
        success: false,
        message: 'Test environment validation failed',
        details: {
          issues: validation.issues,
          warnings: validation.warnings
        }
      };
    }

    logStep('migration', 'Test environment initialized successfully', {
      tablesCreated: results.success.length,
      warnings: validation.warnings.length
    });

    return {
      success: true,
      message: `Test environment initialized with ${results.success.length} tables`,
      details: {
        tablesCreated: results.success,
        warnings: validation.warnings
      }
    };

  } catch (error) {
    logStep('migration', 'Exception during test environment initialization', { 
      error: (error as Error).message 
    });
    
    return {
      success: false,
      message: 'Exception during test environment initialization',
      details: { error: (error as Error).message }
    };
  }
};

/**
 * Sync all test table schemas with their production counterparts
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<MigrationResult> - Result of synchronization
 */
export const syncAllTestTables = async (supabaseClient: SupabaseClient): Promise<MigrationResult> => {
  try {
    logStep('migration', 'Syncing all test table schemas', { tableCount: KNOWN_TABLES.length });

    const results = { success: [] as string[], failed: [] as string[] };

    for (const tableName of KNOWN_TABLES) {
      const syncResult = await syncTestTableSchema(tableName, supabaseClient);
      if (syncResult) {
        results.success.push(tableName);
      } else {
        results.failed.push(tableName);
      }
    }

    if (results.failed.length > 0) {
      return {
        success: false,
        message: `Failed to sync ${results.failed.length} test tables`,
        details: results
      };
    }

    logStep('migration', 'All test table schemas synced successfully', results);

    return {
      success: true,
      message: `Successfully synced ${results.success.length} test tables`,
      details: results
    };

  } catch (error) {
    logStep('migration', 'Exception during test table sync', { 
      error: (error as Error).message 
    });
    
    return {
      success: false,
      message: 'Exception during test table synchronization',
      details: { error: (error as Error).message }
    };
  }
};

/**
 * Validate test environment setup
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<ValidationResult> - Validation results
 */
export const validateTestEnvironment = async (supabaseClient: SupabaseClient): Promise<ValidationResult> => {
  const issues: string[] = [];
  const warnings: string[] = [];

  try {
    logStep('migration', 'Validating test environment', { tableCount: KNOWN_TABLES.length });

    // Check if all test tables exist
    for (const tableName of KNOWN_TABLES) {
      const testTableName = `_test_${tableName}`;
      const exists = await tableExists(testTableName, supabaseClient);
      
      if (!exists) {
        issues.push(`Test table ${testTableName} does not exist`);
        continue;
      }

      // Check if schemas match (basic validation)
      const [baseSchema, testSchema] = await Promise.all([
        getTableSchema(tableName, supabaseClient),
        getTableSchema(testTableName, supabaseClient)
      ]);

      if (!baseSchema) {
        warnings.push(`Base table ${tableName} schema could not be retrieved`);
        continue;
      }

      if (!testSchema) {
        issues.push(`Test table ${testTableName} schema could not be retrieved`);
        continue;
      }

      // Compare column counts
      if (baseSchema.columns.length !== testSchema.columns.length) {
        warnings.push(`Column count mismatch between ${tableName} and ${testTableName}`);
      }

      // Check for missing columns in test table
      const baseColumnNames = baseSchema.columns.map(col => col.columnName);
      const testColumnNames = testSchema.columns.map(col => col.columnName);
      
      const missingColumns = baseColumnNames.filter(name => !testColumnNames.includes(name));
      if (missingColumns.length > 0) {
        warnings.push(`Test table ${testTableName} missing columns: ${missingColumns.join(', ')}`);
      }
    }

    const isValid = issues.length === 0;
    
    logStep('migration', 'Test environment validation completed', {
      isValid,
      issueCount: issues.length,
      warningCount: warnings.length
    });

    return { isValid, issues, warnings };

  } catch (error) {
    logStep('migration', 'Exception during test environment validation', { 
      error: (error as Error).message 
    });
    
    issues.push(`Validation exception: ${(error as Error).message}`);
    return { isValid: false, issues, warnings };
  }
};

/**
 * Reset test environment by cleaning data and optionally recreating tables
 * 
 * @param supabaseClient - Supabase client instance
 * @param recreateTables - Whether to recreate tables after cleanup
 * @returns Promise<MigrationResult> - Result of reset operation
 */
export const resetTestEnvironment = async (
  supabaseClient: SupabaseClient, 
  recreateTables: boolean = false
): Promise<MigrationResult> => {
  try {
    logStep('migration', 'Resetting test environment', { recreateTables });

    // Clean up test data
    const cleanupSuccess = await cleanupTestData(supabaseClient);
    if (!cleanupSuccess) {
      return {
        success: false,
        message: 'Failed to clean up test data'
      };
    }

    if (recreateTables) {
      // Drop and recreate test tables
      const dropResult = await dropAllTestTables(supabaseClient);
      if (!dropResult.success) {
        return {
          success: false,
          message: 'Failed to drop test tables during reset',
          details: dropResult.details
        };
      }

      // Recreate test tables
      const createResult = await createAllTestTables(supabaseClient);
      if (createResult.failed.length > 0) {
        return {
          success: false,
          message: 'Failed to recreate some test tables',
          details: createResult
        };
      }
    }

    logStep('migration', 'Test environment reset completed', { recreateTables });

    return {
      success: true,
      message: recreateTables ? 'Test environment reset and recreated' : 'Test environment data cleaned',
      details: { recreateTables }
    };

  } catch (error) {
    logStep('migration', 'Exception during test environment reset', { 
      error: (error as Error).message 
    });
    
    return {
      success: false,
      message: 'Exception during test environment reset',
      details: { error: (error as Error).message }
    };
  }
};

/**
 * Drop all test tables
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<MigrationResult> - Result of drop operation
 */
export const dropAllTestTables = async (supabaseClient: SupabaseClient): Promise<MigrationResult> => {
  try {
    logStep('migration', 'Dropping all test tables', { tableCount: KNOWN_TABLES.length });

    const results = { success: [] as string[], failed: [] as string[] };

    for (const tableName of KNOWN_TABLES) {
      const testTableName = `_test_${tableName}`;
      
      try {
        const { error } = await supabaseClient.rpc('exec_sql', { 
          sql: `DROP TABLE IF EXISTS "${testTableName}" CASCADE;` 
        });
        
        if (error) {
          logStep('migration', 'Error dropping test table', { testTableName, error: error.message });
          results.failed.push(testTableName);
        } else {
          results.success.push(testTableName);
        }
      } catch (error) {
        logStep('migration', 'Exception dropping test table', { 
          testTableName, 
          error: (error as Error).message 
        });
        results.failed.push(testTableName);
      }
    }

    const success = results.failed.length === 0;
    
    logStep('migration', 'Test table drop operation completed', results);

    return {
      success,
      message: success 
        ? `Successfully dropped ${results.success.length} test tables`
        : `Failed to drop ${results.failed.length} test tables`,
      details: results
    };

  } catch (error) {
    logStep('migration', 'Exception during test table drop operation', { 
      error: (error as Error).message 
    });
    
    return {
      success: false,
      message: 'Exception during test table drop operation',
      details: { error: (error as Error).message }
    };
  }
};

/**
 * Get migration status for test environment
 * 
 * @param supabaseClient - Supabase client instance
 * @returns Promise<object> - Status information
 */
export const getTestEnvironmentStatus = async (supabaseClient: SupabaseClient) => {
  try {
    const status = {
      tablesStatus: {} as Record<string, { exists: boolean, hasData: boolean }>,
      validation: await validateTestEnvironment(supabaseClient),
      summary: {
        totalTables: KNOWN_TABLES.length,
        existingTables: 0,
        tablesWithData: 0
      }
    };

    // Check each table
    for (const tableName of KNOWN_TABLES) {
      const testTableName = `_test_${tableName}`;
      const exists = await tableExists(testTableName, supabaseClient);
      
      let hasData = false;
      if (exists) {
        try {
          const { count } = await supabaseClient
            .from(testTableName)
            .select('*', { count: 'exact', head: true });
          hasData = (count || 0) > 0;
        } catch (error) {
          // Ignore count errors
        }
      }

      status.tablesStatus[tableName] = { exists, hasData };
      
      if (exists) status.summary.existingTables++;
      if (hasData) status.summary.tablesWithData++;
    }

    return status;

  } catch (error) {
    logStep('migration', 'Exception getting test environment status', { 
      error: (error as Error).message 
    });
    
    return {
      error: (error as Error).message,
      tablesStatus: {},
      validation: { isValid: false, issues: ['Status check failed'], warnings: [] },
      summary: { totalTables: 0, existingTables: 0, tablesWithData: 0 }
    };
  }
};

/**
 * Export types for use in other modules
 */
export type { MigrationResult, ValidationResult };
