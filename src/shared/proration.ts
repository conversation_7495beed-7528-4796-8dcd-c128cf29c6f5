// Proration and trial management utilities
// Handles subscription billing calculations and trial period management

import { createStripeClient } from "./stripe";
import { logStep } from "./logger";

export interface ProrationCalculation {
  currentAmount: number;
  newAmount: number;
  prorationAmount: number;
  creditAmount: number;
  nextInvoiceAmount: number;
  effectiveDate: Date;
}

export interface TrialInfo {
  isInTrial: boolean;
  trialStart?: Date;
  trialEnd?: Date;
  daysRemaining?: number;
  canExtend: boolean;
}

// Calculate proration for subscription changes
export const calculateProration = async (
  subscriptionId: string,
  newPriceId: string,
  effectiveDate?: Date
): Promise<ProrationCalculation> => {
  try {
    const stripe = createStripeClient();
    
    // Get current subscription
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const currentPrice = subscription.items.data[0].price;
    
    // Calculate proration using <PERSON><PERSON>'s upcoming invoice preview
    const upcomingInvoice = await stripe.invoices.retrieveUpcoming({
      customer: subscription.customer as string,
      subscription: subscriptionId,
      subscription_items: [{
        id: subscription.items.data[0].id,
        price: newPriceId
      }],
      subscription_proration_behavior: 'create_prorations',
      subscription_proration_date: effectiveDate ? Math.floor(effectiveDate.getTime() / 1000) : undefined
    });
    
    // Calculate amounts
    const currentAmount = currentPrice.unit_amount || 0;
    const newPrice = await stripe.prices.retrieve(newPriceId);
    const newAmount = newPrice.unit_amount || 0;
    
    // Find proration line items
    const prorationItems = upcomingInvoice.lines.data.filter(item => item.proration);
    const prorationAmount = prorationItems.reduce((sum, item) => sum + item.amount, 0);
    
    // Calculate credit (negative proration means credit)
    const creditAmount = prorationAmount < 0 ? Math.abs(prorationAmount) : 0;
    
    return {
      currentAmount,
      newAmount,
      prorationAmount,
      creditAmount,
      nextInvoiceAmount: upcomingInvoice.amount_due,
      effectiveDate: effectiveDate || new Date()
    };
    
  } catch (error) {
    logStep("proration", "Error calculating proration", { error: (error as Error).message });
    throw new Error(`Failed to calculate proration: ${(error as Error).message}`);
  }
};

// Get trial information for a subscription
export const getTrialInfo = async (subscriptionId: string): Promise<TrialInfo> => {
  try {
    const stripe = createStripeClient();
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    const now = new Date();
    const trialStart = subscription.trial_start ? new Date(subscription.trial_start * 1000) : undefined;
    const trialEnd = subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined;
    
    const isInTrial = trialEnd ? trialEnd > now : false;
    const daysRemaining = isInTrial && trialEnd ? 
      Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0;
    
    // Can extend trial if currently in trial and less than 14 days total
    const canExtend = isInTrial && trialStart && trialEnd &&
      ((trialEnd.getTime() - trialStart.getTime()) / (1000 * 60 * 60 * 24)) < 14;
    
    return {
      isInTrial,
      trialStart,
      trialEnd,
      daysRemaining,
      canExtend
    };
    
  } catch (error) {
    logStep("trial", "Error getting trial info", { error: (error as Error).message });
    throw new Error(`Failed to get trial info: ${(error as Error).message}`);
  }
};

// Extend trial period
export const extendTrial = async (
  subscriptionId: string,
  additionalDays: number
): Promise<{ success: boolean; newTrialEnd: Date }> => {
  try {
    const stripe = createStripeClient();
    
    // Get current trial info
    const trialInfo = await getTrialInfo(subscriptionId);
    
    if (!trialInfo.isInTrial || !trialInfo.trialEnd) {
      throw new Error("Subscription is not currently in trial");
    }
    
    if (!trialInfo.canExtend) {
      throw new Error("Trial cannot be extended further");
    }
    
    // Calculate new trial end date
    const newTrialEnd = new Date(trialInfo.trialEnd);
    newTrialEnd.setDate(newTrialEnd.getDate() + additionalDays);
    
    // Update subscription in Stripe
    await stripe.subscriptions.update(subscriptionId, {
      trial_end: Math.floor(newTrialEnd.getTime() / 1000)
    });
    
    logStep("trial", "Trial extended successfully", {
      subscriptionId,
      additionalDays,
      newTrialEnd: newTrialEnd.toISOString()
    });
    
    return {
      success: true,
      newTrialEnd
    };
    
  } catch (error) {
    logStep("trial", "Error extending trial", { error: (error as Error).message });
    throw new Error(`Failed to extend trial: ${(error as Error).message}`);
  }
};

// Create subscription with trial
export const createSubscriptionWithTrial = async (
  customerId: string,
  priceId: string,
  trialDays: number = 7,
  metadata: Record<string, string> = {}
): Promise<string> => {
  try {
    const stripe = createStripeClient();
    
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + trialDays);
    
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      trial_end: Math.floor(trialEnd.getTime() / 1000),
      metadata: {
        ...metadata,
        trial_days: trialDays.toString(),
        trial_created: new Date().toISOString()
      }
    });
    
    logStep("trial", "Subscription with trial created", {
      subscriptionId: subscription.id,
      trialDays,
      trialEnd: trialEnd.toISOString()
    });
    
    return subscription.id;
    
  } catch (error) {
    logStep("trial", "Error creating subscription with trial", { error: (error as Error).message });
    throw new Error(`Failed to create subscription with trial: ${(error as Error).message}`);
  }
};

// Calculate upgrade/downgrade timing
export const calculateChangeEffectiveDate = (
  isUpgrade: boolean,
  currentPeriodEnd: Date,
  immediate: boolean = false
): Date => {
  if (immediate || isUpgrade) {
    // Upgrades are effective immediately
    return new Date();
  } else {
    // Downgrades are effective at the end of the current period
    return currentPeriodEnd;
  }
};

// Validate proration settings
export const validateProrationBehavior = (
  behavior: string
): 'create_prorations' | 'none' | 'always_invoice' => {
  const validBehaviors = ['create_prorations', 'none', 'always_invoice'];
  if (!validBehaviors.includes(behavior)) {
    return 'create_prorations'; // Default
  }
  return behavior as 'create_prorations' | 'none' | 'always_invoice';
};

// Calculate refund amount for cancellation
export const calculateCancellationRefund = async (
  subscriptionId: string,
  cancellationDate?: Date
): Promise<{ refundAmount: number; refundEligible: boolean }> => {
  try {
    const stripe = createStripeClient();
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    const cancelDate = cancellationDate || new Date();
    const periodStart = new Date(subscription.current_period_start * 1000);
    const periodEnd = new Date(subscription.current_period_end * 1000);
    
    // Calculate unused portion of the billing period
    const totalPeriodDays = (periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24);
    const usedDays = (cancelDate.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24);
    const unusedDays = Math.max(0, totalPeriodDays - usedDays);
    
    const unusedPortion = unusedDays / totalPeriodDays;
    const subscriptionAmount = subscription.items.data[0].price.unit_amount || 0;
    const refundAmount = Math.floor(subscriptionAmount * unusedPortion);
    
    // Only eligible for refund if more than 7 days remaining and amount > $5
    const refundEligible = unusedDays > 7 && refundAmount > 500; // $5 in cents
    
    return {
      refundAmount: refundEligible ? refundAmount : 0,
      refundEligible
    };
    
  } catch (error) {
    logStep("refund", "Error calculating cancellation refund", { error: (error as Error).message });
    return { refundAmount: 0, refundEligible: false };
  }
};

// Helper to format proration amounts for display
export const formatProrationDisplay = (calculation: ProrationCalculation) => {
  const formatCents = (cents: number) => (cents / 100).toFixed(2);
  
  return {
    currentAmount: `$${formatCents(calculation.currentAmount)}`,
    newAmount: `$${formatCents(calculation.newAmount)}`,
    prorationAmount: calculation.prorationAmount >= 0 
      ? `+$${formatCents(calculation.prorationAmount)}` 
      : `-$${formatCents(Math.abs(calculation.prorationAmount))}`,
    creditAmount: calculation.creditAmount > 0 ? `$${formatCents(calculation.creditAmount)}` : null,
    nextInvoiceAmount: `$${formatCents(calculation.nextInvoiceAmount)}`,
    isUpgrade: calculation.newAmount > calculation.currentAmount,
    isDowngrade: calculation.newAmount < calculation.currentAmount
  };
};
