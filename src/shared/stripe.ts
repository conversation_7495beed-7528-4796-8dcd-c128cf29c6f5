import Stripe from "stripe";
import { logStep } from "./logger";

// Export Stripe type for use in other modules
export { Stripe };

export const createStripeClient = () => {
  const isProduction = process.env.NODE_ENV === "production";
  const stripeKey = isProduction 
    ? process.env.STRIPE_LIVE_SECRET_KEY
    : process.env.STRIPE_TEST_SECRET_KEY;
  // Log the stripe key using logStep
  logStep("stripe", "Creating Stripe client", { stripeKey });
  return new Stripe(stripeKey ?? "", {
    apiVersion: "2025-02-24.acacia",
  });
}; 