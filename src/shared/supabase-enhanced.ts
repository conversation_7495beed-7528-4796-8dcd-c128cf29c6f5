import { createClient, type SupabaseClient } from "@supabase/supabase-js";
import type { Context } from "hono";
import { logStep } from "./logger";
import { getEnvironmentContext, type EnvironmentContext } from "../middleware/environment";
import { getTableName, ensureTestTable } from "./database-utils";

/**
 * Enhanced Supabase client that automatically handles environment-aware table names
 */
export class EnvironmentAwareSupabaseClient {
  private client: SupabaseClient;
  private environmentContext: EnvironmentContext;

  constructor(client: SupabaseClient, environmentContext: EnvironmentContext) {
    this.client = client;
    this.environmentContext = environmentContext;
  }

  /**
   * Environment-aware from() method that automatically uses correct table names
   * 
   * @param tableName - Base table name
   * @returns Supabase query builder with correct table name
   */
  from(tableName: string) {
    const actualTableName = getTableName(tableName, this.environmentContext);
    
    logStep('supabase', 'Using environment-aware table name', {
      baseTableName: tableName,
      actualTableName,
      environment: this.environmentContext.environment
    });

    return this.client.from(actualTableName);
  }

  /**
   * Environment-aware RPC method
   * For stored procedures that need to work with test tables, we may need
   * to pass the environment context as a parameter
   * 
   * @param fn - Function name
   * @param args - Function arguments
   * @returns RPC result
   */
  rpc(fn: string, args?: Record<string, any>) {
    // Add environment context to RPC calls if needed
    const enhancedArgs = {
      ...args,
      _environment: this.environmentContext.environment,
      _table_prefix: this.environmentContext.tablePrefix
    };

    logStep('supabase', 'Calling RPC with environment context', {
      function: fn,
      environment: this.environmentContext.environment,
      hasArgs: !!args
    });

    return this.client.rpc(fn, enhancedArgs);
  }

  /**
   * Ensure test table exists before performing operations
   * 
   * @param tableName - Base table name
   * @returns Promise<boolean> - true if table exists or was created
   */
  async ensureTestTable(tableName: string): Promise<boolean> {
    if (!this.environmentContext.isTestEnvironment) {
      return true; // No need to ensure test tables in production
    }

    return await ensureTestTable(tableName, this.client);
  }

  /**
   * Get the underlying Supabase client for operations that don't need environment awareness
   */
  get rawClient(): SupabaseClient {
    return this.client;
  }

  /**
   * Get the current environment context
   */
  get environment(): EnvironmentContext {
    return this.environmentContext;
  }

  /**
   * Proxy all other Supabase client methods
   */
  get auth() {
    return this.client.auth;
  }

  get storage() {
    return this.client.storage;
  }

  get realtime() {
    return this.client.realtime;
  }

  get functions() {
    return this.client.functions;
  }
}

/**
 * Create an environment-aware Supabase client
 * 
 * @param authToken - Optional auth token
 * @param environmentContext - Environment context
 * @returns Enhanced Supabase client
 */
export const createEnvironmentAwareSupabaseClient = (
  authToken?: string, 
  environmentContext?: EnvironmentContext
): EnvironmentAwareSupabaseClient => {
  const client = createClient(
    process.env.SUPABASE_URL ?? "",
    process.env.SUPABASE_SERVICE_ROLE_KEY ?? "",
    authToken
      ? {
          global: {
            headers: {
              Authorization: authToken,
            },
          },
        }
      : { auth: { persistSession: false } }
  );

  // Use provided context or default to production
  const context = environmentContext || {
    environment: 'production' as const,
    tablePrefix: '',
    isTestEnvironment: false
  };

  return new EnvironmentAwareSupabaseClient(client, context);
};

/**
 * Create environment-aware Supabase client from Hono context
 * 
 * @param c - Hono context
 * @param authToken - Optional auth token (if not provided, will try to get from context)
 * @returns Enhanced Supabase client
 */
export const createEnvironmentAwareSupabaseClientFromContext = (
  c: Context, 
  authToken?: string
): EnvironmentAwareSupabaseClient => {
  const environmentContext = getEnvironmentContext(c);
  const token = authToken || c.req.header("authorization");
  
  return createEnvironmentAwareSupabaseClient(token, environmentContext);
};

/**
 * Backward compatibility: Create regular Supabase client (unchanged from original)
 * This maintains compatibility with existing code that doesn't need environment awareness
 */
export const createSupabaseClient = (authToken?: string) => {
  return createClient(
    process.env.SUPABASE_URL ?? "",
    process.env.SUPABASE_SERVICE_ROLE_KEY ?? "",
    authToken
      ? {
          global: {
            headers: {
              Authorization: authToken,
            },
          },
        }
      : { auth: { persistSession: false } }
  );
};

/**
 * Backward compatibility: Create Supabase client from context (unchanged from original)
 */
export const createSupabaseClientFromContext = (c: Context) => {
  const authHeader = c.req.header("authorization");
  return createSupabaseClient(authHeader);
};

/**
 * Helper function to automatically ensure test tables exist for common operations
 * 
 * @param client - Environment-aware Supabase client
 * @param tableNames - Array of table names to ensure
 * @returns Promise<boolean> - true if all tables exist or were created
 */
export const ensureTestTablesExist = async (
  client: EnvironmentAwareSupabaseClient, 
  tableNames: string[]
): Promise<boolean> => {
  if (!client.environment.isTestEnvironment) {
    return true; // No need in production
  }

  logStep('supabase', 'Ensuring test tables exist', { tableNames });

  const results = await Promise.all(
    tableNames.map(tableName => client.ensureTestTable(tableName))
  );

  const allSuccessful = results.every(result => result);
  
  if (!allSuccessful) {
    logStep('supabase', 'Some test tables failed to be created', { tableNames, results });
  }

  return allSuccessful;
};

/**
 * Utility function to get table name for current environment
 * Useful for raw SQL queries or other operations that need the actual table name
 * 
 * @param baseTableName - Base table name
 * @param c - Hono context
 * @returns Actual table name for current environment
 */
export const getEnvironmentTableName = (baseTableName: string, c: Context): string => {
  const environmentContext = getEnvironmentContext(c);
  return getTableName(baseTableName, environmentContext);
};

/**
 * Type definitions for enhanced client
 */
export type { EnvironmentAwareSupabaseClient };

/**
 * Re-export original functions for backward compatibility
 */
export { getAuthenticatedUser, ensureUserSubscription } from './supabase';
