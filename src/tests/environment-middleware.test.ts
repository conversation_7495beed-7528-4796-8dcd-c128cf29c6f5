/**
 * Test suite for environment middleware and database utilities
 * 
 * This file contains comprehensive tests to verify that the test environment
 * middleware and database functions work correctly in both test and production environments.
 */

import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { Hono } from 'hono';
import { 
  environmentMiddleware, 
  getEnvironmentContext, 
  isTestEnvironment, 
  getTableName,
  ENVIRONMENT_VALUES,
  ENVIRONMENT_HEADERS
} from '../middleware/environment';
import { 
  createEnvironmentAwareSupabaseClient,
  createEnvironmentAwareSupabaseClientFromContext 
} from '../shared/supabase-enhanced';
import { 
  getTableName as dbGetTableName,
  tableExists,
  createTestTable,
  ensureTestTable,
  KNOWN_TABLES
} from '../shared/database-utils';
import { 
  initializeTestEnvironment,
  validateTestEnvironment,
  resetTestEnvironment,
  getTestEnvironmentStatus
} from '../shared/migration-utils';

// Mock Supabase client for testing
const mockSupabaseClient = {
  from: (tableName: string) => ({
    select: () => ({ eq: () => ({ single: () => Promise.resolve({ data: null, error: null }) }) }),
    insert: () => ({ select: () => ({ single: () => Promise.resolve({ data: { id: 'test-id' }, error: null }) }) }),
    update: () => ({ eq: () => Promise.resolve({ data: null, error: null }) }),
    delete: () => ({ neq: () => Promise.resolve({ data: null, error: null }) })
  }),
  rpc: () => Promise.resolve({ data: null, error: null }),
  auth: {},
  storage: {},
  realtime: {},
  functions: {}
};

describe('Environment Middleware', () => {
  let app: Hono;

  beforeEach(() => {
    app = new Hono();
    app.use('*', environmentMiddleware);
  });

  it('should detect test environment from x-env header', async () => {
    app.get('/test', (c) => {
      const context = getEnvironmentContext(c);
      return c.json(context);
    });

    const req = new Request('http://localhost/test', {
      headers: { 'x-env': 'test' }
    });

    const res = await app.request(req);
    const data = await res.json();

    expect(data.environment).toBe('test');
    expect(data.tablePrefix).toBe('_test_');
    expect(data.isTestEnvironment).toBe(true);
  });

  it('should default to production environment when no header', async () => {
    app.get('/test', (c) => {
      const context = getEnvironmentContext(c);
      return c.json(context);
    });

    const req = new Request('http://localhost/test');
    const res = await app.request(req);
    const data = await res.json();

    expect(data.environment).toBe('production');
    expect(data.tablePrefix).toBe('');
    expect(data.isTestEnvironment).toBe(false);
  });

  it('should use production environment for x-env=live', async () => {
    app.get('/test', (c) => {
      const context = getEnvironmentContext(c);
      return c.json(context);
    });

    const req = new Request('http://localhost/test', {
      headers: { 'x-env': 'live' }
    });

    const res = await app.request(req);
    const data = await res.json();

    expect(data.environment).toBe('production');
    expect(data.tablePrefix).toBe('');
    expect(data.isTestEnvironment).toBe(false);
  });

  it('should provide helper functions for environment detection', async () => {
    app.get('/test', (c) => {
      const isTest = isTestEnvironment(c);
      const tableName = getTableName('users', c);
      return c.json({ isTest, tableName });
    });

    // Test environment
    const testReq = new Request('http://localhost/test', {
      headers: { 'x-env': 'test' }
    });
    const testRes = await app.request(testReq);
    const testData = await testRes.json();

    expect(testData.isTest).toBe(true);
    expect(testData.tableName).toBe('_test_users');

    // Production environment
    const prodReq = new Request('http://localhost/test');
    const prodRes = await app.request(prodReq);
    const prodData = await prodRes.json();

    expect(prodData.isTest).toBe(false);
    expect(prodData.tableName).toBe('users');
  });
});

describe('Database Utilities', () => {
  const testContext = {
    environment: 'test' as const,
    tablePrefix: '_test_',
    isTestEnvironment: true
  };

  const prodContext = {
    environment: 'production' as const,
    tablePrefix: '',
    isTestEnvironment: false
  };

  it('should generate correct table names for different environments', () => {
    expect(dbGetTableName('users', testContext)).toBe('_test_users');
    expect(dbGetTableName('messages', testContext)).toBe('_test_messages');
    expect(dbGetTableName('users', prodContext)).toBe('users');
    expect(dbGetTableName('messages', prodContext)).toBe('messages');
  });

  it('should handle edge cases in table name generation', () => {
    expect(dbGetTableName('', testContext)).toBe('_test_');
    expect(dbGetTableName('table_with_underscores', testContext)).toBe('_test_table_with_underscores');
    expect(dbGetTableName('CamelCaseTable', testContext)).toBe('_test_CamelCaseTable');
  });
});

describe('Environment-Aware Supabase Client', () => {
  it('should create client with correct environment context', () => {
    const testContext = {
      environment: 'test' as const,
      tablePrefix: '_test_',
      isTestEnvironment: true
    };

    const client = createEnvironmentAwareSupabaseClient(undefined, testContext);
    
    expect(client.environment.environment).toBe('test');
    expect(client.environment.tablePrefix).toBe('_test_');
    expect(client.environment.isTestEnvironment).toBe(true);
  });

  it('should default to production environment when no context provided', () => {
    const client = createEnvironmentAwareSupabaseClient();
    
    expect(client.environment.environment).toBe('production');
    expect(client.environment.tablePrefix).toBe('');
    expect(client.environment.isTestEnvironment).toBe(false);
  });

  it('should provide access to underlying client methods', () => {
    const client = createEnvironmentAwareSupabaseClient();
    
    expect(client.auth).toBeDefined();
    expect(client.storage).toBeDefined();
    expect(client.realtime).toBeDefined();
    expect(client.functions).toBeDefined();
    expect(client.rawClient).toBeDefined();
  });
});

describe('Integration Tests', () => {
  let app: Hono;

  beforeEach(() => {
    app = new Hono();
    app.use('*', environmentMiddleware);
  });

  it('should handle complete request flow with test environment', async () => {
    app.post('/api/test', async (c) => {
      const client = createEnvironmentAwareSupabaseClientFromContext(c);
      
      // Simulate database operation
      const tableName = client.environment.isTestEnvironment ? '_test_users' : 'users';
      
      return c.json({
        environment: client.environment.environment,
        tableName,
        isTest: client.environment.isTestEnvironment
      });
    });

    const req = new Request('http://localhost/api/test', {
      method: 'POST',
      headers: { 
        'x-env': 'test',
        'content-type': 'application/json'
      },
      body: JSON.stringify({ test: 'data' })
    });

    const res = await app.request(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.environment).toBe('test');
    expect(data.tableName).toBe('_test_users');
    expect(data.isTest).toBe(true);
  });

  it('should handle complete request flow with production environment', async () => {
    app.post('/api/test', async (c) => {
      const client = createEnvironmentAwareSupabaseClientFromContext(c);
      
      return c.json({
        environment: client.environment.environment,
        tableName: client.environment.isTestEnvironment ? '_test_users' : 'users',
        isTest: client.environment.isTestEnvironment
      });
    });

    const req = new Request('http://localhost/api/test', {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ test: 'data' })
    });

    const res = await app.request(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.environment).toBe('production');
    expect(data.tableName).toBe('users');
    expect(data.isTest).toBe(false);
  });
});

describe('Constants and Types', () => {
  it('should export correct environment constants', () => {
    expect(ENVIRONMENT_VALUES.TEST).toBe('test');
    expect(ENVIRONMENT_VALUES.PRODUCTION).toBe('production');
    expect(ENVIRONMENT_VALUES.LIVE).toBe('live');
  });

  it('should export correct header constants', () => {
    expect(ENVIRONMENT_HEADERS.X_ENV).toBe('x-env');
  });

  it('should include all expected tables in KNOWN_TABLES', () => {
    const expectedTables = [
      'user_subscriptions',
      'characters',
      'conversations',
      'messages',
      'waitlist',
      'invite_codes',
      'subscription_metrics',
      'subscription_change_history'
    ];

    expectedTables.forEach(table => {
      expect(KNOWN_TABLES).toContain(table);
    });
  });
});

// Note: Database integration tests would require actual Supabase connection
// These would be run separately in an integration test environment
describe('Database Integration Tests (Requires Supabase)', () => {
  it.skip('should create test tables successfully', async () => {
    // This test would require actual Supabase connection
    // Implementation would test actual table creation
  });

  it.skip('should sync table schemas correctly', async () => {
    // This test would require actual Supabase connection
    // Implementation would test schema synchronization
  });

  it.skip('should validate test environment setup', async () => {
    // This test would require actual Supabase connection
    // Implementation would test environment validation
  });
});
